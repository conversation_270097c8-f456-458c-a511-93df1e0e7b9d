# Siflowtype 开发文档

**版本**: 1.0
**文档生成时间**: 2025-07-23

## 1. 系统核心价值与架构

Siflowtype 是一款专为开发者设计的 AI 英文输入法，旨在将用户输入的碎片化中英文、术语、甚至代码片段实时转化为语法正确、表达地道的高质量英文句子。其核心架构围绕着几个关键模块构建，实现了清晰的职责分离：

-   **`用户输入处理 (InputController)`**: 作为系统与操作系统的接口，负责捕获键盘事件。
-   **`核心交互逻辑 (InputEngine)`**: 系统的“大脑”，处理输入事件、管理核心状态、编排 AI 调用和 UI 更新。
-   **`候选词面板 (Candidate Panel)`**: 系统的核心 UI，由 `CandidateViewModel` (状态管理) 和 `CandidateBoardView` (视图渲染) 组成，负责展示加载、结果和错误等状态。
-   **`应用代理 (ApplicationDelegate)`**: 管理应用的生命周期和全局组件，如状态栏图标和候选词面板窗口的创建与管理。

## 2. 核心功能实现：从按键到 AI 建议

让我们以“用户输入文本后按 `Enter` 键获取 AI 建议”这个核心用户故事为例，追踪基于最新 MVVM 架构的交互流程。

**核心数据流**: `IMK Event` → `SiflowtypeInputController` → `InputViewModel` → `InputEngine` → `InputViewModel` (更新 `@Published` 属性) → `CandidateBoardView` / `SiflowtypeInputController` (响应式更新 UI)

### 步骤 1: 捕获键盘事件并转发

用户的每一次按键首先由 `InputMethodKit` 框架捕获，并传递给我们的 <mcsymbol name="SiflowtypeInputController" filename="SiflowtypeInputController.swift" path="/Users/<USER>/projects/siflow/Siflowtype/Features/InputHandling/SiflowtypeInputController.swift" startline="4" type="class"></mcsymbol>。`InputController` 的职责非常单一：它将 IMK 事件直接转发给共享的 <mcsymbol name="InputViewModel" filename="InputViewModel.swift" path="/Users/<USER>/projects/siflow/Siflowtype/Features/InputHandling/InputViewModel.swift" startline="20" type="class"></mcsymbol> 进行处理。

### 步骤 2: ViewModel 处理业务逻辑并触发 AI 调用

<mcsymbol name="InputViewModel" filename="InputViewModel.swift" path="/Users/<USER>/projects/siflow/Siflowtype/Features/InputHandling/InputViewModel.swift" startline="20" type="class"></mcsymbol> 是业务逻辑的核心协调者。当接收到 `Enter` 键事件时：

1.  **更新 UI 状态**: ViewModel 立即将其内部的 `@Published` 属性 `uiState` 更新为 `.loading`。由于 `CandidateBoardView` 订阅了这个属性，UI 会自动响应式地显示一个加载指示器，为用户提供即时反馈。
2.  **调用核心引擎**: ViewModel 调用 <mcsymbol name="InputEngine" filename="InputEngine.swift" path="/Users/<USER>/projects/siflow/Siflowtype/Features/InputHandling/InputEngine.swift" startline="90" type="class"></mcsymbol>。`InputEngine` 现在是一个纯粹的计算单元，它异步地执行 AI 请求（通过 `OpenAIService`），并返回一个结果（成功或失败），而不再直接与任何 UI 组件交互。

### 步骤 3: ViewModel 处理 AI 结果并更新 UI

当 `InputEngine` 的异步方法返回结果后，`InputViewModel` 在其回调中处理：

-   **成功**: ViewModel 使用返回的建议更新其内部的 `@Published` 属性 `candidateItems`，并将 `uiState` 切换到 `.displayingCandidates`。
-   **失败**: ViewModel 使用返回的错误信息更新 `uiState` 到 `.error`。

由于 `CandidateBoardView` 订阅了 `uiState` 和 `candidateItems`，SwiftUI 会自动、高效地更新视图，无论是显示候选列表还是错误提示。这种单向数据流极大地简化了状态管理，并消除了潜在的循环依赖。

## 3. 输入模式切换：AI 建议 vs. 纯英文

为了满足不同场景下的输入需求，系统实现了一个核心的输入模式切换功能，允许用户在“AI辅助模式”和“纯英文直出模式”之间自由选择。

### 3.1. 模式定义 (`InputMode`)

模式状态由 <mcsymbol name="InputViewModel" filename="InputViewModel.swift" path="/Users/<USER>/projects/siflow/Siflowtype/Features/InputHandling/InputViewModel.swift" startline="20" type="class"></mcsymbol> 中的 `InputMode` 枚举管理，作为所有输入行为的顶层开关：

-   **`.ai` (默认模式)**: 在此模式下，用户的输入被视为"预编辑文本"，按下 `Enter` 键会触发 AI 服务，获取英文润色建议。候选词面板会根据 `CandidatePanelState` 状态机正常显示加载、候选列表或错误状态。
-   **`.english` (纯英文模式)**: 在此模式下，Siflowtype 的行为类似一个标准的英文键盘。所有字符输入被直接提交到当前应用程序（即“直接上屏”），`Enter` 键执行其默认的换行操作。候选词面板将始终保持隐藏。

### 3.2. 模式切换机制

系统提供两种方式供用户切换输入模式，以确保易用性和可发现性：

1.  **快捷键切换与 UI 提示**: 
    - **切换操作**: 用户可以通过按下 `Shift` 键（在无任何输入时）来快速循环切换 `.ai` 和 `.english` 模式。这是一个高效的“盲操作”选项。
    - **视觉反馈**: 为了让用户明确感知到模式的切换，当 `Shift` 键被按下时，屏幕中央会短暂地（约1秒）显示一个半透明的黑色方块，其中包含清晰的模式指示符：
        - 切换到 AI 模式时，显示 **“AI”** 字符。
        - 切换到纯英文模式时，显示 **“A”** 字符。
    - **实现方案**: 系统创建了一个 `ModeIndicatorPanel`，它是一个无边框、不激活的 `NSPanel`，用于承载显示模式的 `ModeIndicatorView` (SwiftUI View)。`AppCoordinator` 负责实例化并持有这个 Panel，而 `InputViewModel` 在检测到 `Shift` 键操作时，会调用相应的方法来显示这个提示。

2.  **菜单栏切换**: 在状态栏的Siflowtype菜单中，我们会添加一个明确的菜单项，例如“输入模式：AI 建议”，并带有一个对勾指示当前状态。用户可以点击此菜单项来切换到另一种模式（例如，变为“输入模式：纯英文”）。这为新用户提供了清晰的视觉引导。

用户的选择通过 `UserDefaults` 进行持久化存储，确保应用重启后用户的习惯得以保留。

### 3.3. 核心设计原则：稳定性优先

**任务指标**：Agent 在实现所有功能时，必须严格遵守以下核心原则：

1.  **`.english` 模式是基石**：此模式代表了最基础、最核心的键盘输入功能。它必须是绝对稳定、可靠且无延迟的。任何对 `InputEngine` 的修改，都不能以任何方式影响或损害此模式下的输入体验。
2.  **`.ai` 模式是增强**：AI 辅助功能是建立在稳定基石之上的“增强层”。其设计和实现必须确保，即时在 AI 服务出现延迟、错误或异常的情况下，用户也能无缝、即时地回退到基础输入模式，或取消 AI 操作，而不会丢失任何输入或感到卡顿。

简而言之，**效率提升的前提是基础功能的绝对可靠**。如果最基本的打字功能都无法保证，那么 AI 辅助将毫无意义。这一原则是 Siflowtype 开发的最高优先级。

### 3.4. 核心逻辑实现

此设计的实现完全遵循 MVVM 架构，主要集中在 `InputViewModel` 中：

-   `InputViewModel` 持有 `@Published` 的 `currentMode` 属性，其值从 `UserDefaults` 初始化。
-   所有输入事件处理方法（如 `handleModeToggleKeyEvent`）的入口处，会首先检查 `currentMode`。
-   如果 `currentMode` 是 `.english`，则所有输入事件都将直接调用 `delegate` 的 `commitText` 方法，绕过所有后续的 AI 调用和状态机逻辑。
-   如果 `currentMode` 是 `.ai`，则执行 AI 辅助流程。
-   处理 `Shift` 键的逻辑用于切换模式、更新 `UserDefaults`，并调用 `AppCoordinator` 上的方法来显示模式切换UI提示。
-   **组合键处理**: `Shift` 键的模式切换功能仅在其被**单独按下并释放**时触发，且当前无预编辑文本。当它与其他键组合使用时，将维持其作为修饰键的原始功能。

通过这种方式，我们以最小的侵入性为系统增加了一个强大的控制层，使用户能够根据上下文精确地控制输入法的行为。

## 4. 关键设计与代码解析

### 4.1. 统一状态管理：`InputViewModel`

根据最新的架构改进，系统采用了更彻底的 MVVM 模式。原有的 `InputState` 和 `CandidatePanelState` 已被统一整合到 <mcsymbol name="InputViewModel" filename="InputViewModel.swift" path="/Users/<USER>/projects/siflow/Siflowtype/Features/InputHandling/InputViewModel.swift" startline="20" type="class"></mcsymbol> 中。

-   **`InputViewModel`**: 作为单一信源 (Single Source of Truth)，它同时管理着输入的核心数据（如预编辑文本、候选词）和驱动 UI 的状态（如 `.typing`, `.loading`, `.displayingCandidates`, `.error`）。所有状态都通过 `@Published` 属性暴露给 SwiftUI 视图。

这种统一管理消除了之前数据与 UI 状态的分离，创建了一个清晰、单向的数据流，使得 `InputEngine` 可以专注于纯粹的计算，而所有状态变更和业务逻辑都集中在 ViewModel 中，代码更易于维护和测试。

### 4.2. 配置管理

- **国际化(i18n)**: **必须优先使用字符串目录 (`.xcstrings`) 文件**进行所有文本的本地化。它提供了类型安全、编译时检查和集成的编辑器支持，是现代 Xcode 项目的首选方案。传统的 `.strings` 文件仅用于兼容旧版项目。
- **静态配置**: 应用的基本配置（如 Bundle Identifier, 版本号）在 `Info.plist` 中管理。



### 4.3. 通过代理模式与 ViewModel 解耦

`InputViewModel` 需要在某些事件发生时（如需要上屏文本）通知 `SiflowtypeInputController`。为了避免直接引用导致的紧耦合，系统使用了 `InputViewModelDelegate` 协议。`InputViewModel` 持有一个 `delegate`，而 `SiflowtypeInputController` 遵循这个协议并实现其方法。当 `InputViewModel` 需要执行提交文本等操作时，它只需调用 `delegate` 的方法，而无需关心具体的实现者是谁，这是一种非常经典的解耦模式。

### 4.4. UI状态机设计 (`CandidatePanelState`)

`CandidatePanelState` 不仅仅是一个简单的枚举，它实际上构成了一个微型的状态机，精确地控制了候选词面板的所有视觉呈现和交互行为。该状态机现在由 `CandidateViewModel` 统一管理，确保了用户界面在任何时候都处于一个明确、一致且可预测的状态。

状态转换图如下：

```mermaid
stateDiagram-v2
    [*] --> Typing

    Typing --> Loading: 用户按 Enter 键 (ViewModel 更新状态)
    Loading --> DisplayingCandidates: AI 成功返回建议 (ViewModel 更新状态)
    Loading --> Error: AI 调用失败或超时 (ViewModel 更新状态)
    
    DisplayingCandidates --> Typing: 用户选择候选项或清空输入
    Error --> Typing: 用户按 Esc 或 Backspace 清除错误
```

-   **`Typing`**: 初始状态。用户正在输入，面板可能隐藏或显示预编辑文本。
-   **`Loading`**: 过渡状态。当用户触发 AI 功能后，`InputViewModel` 进入此状态，UI 会显示加载指示器，并锁定输入，直到 AI 返回结果。
-   **`DisplayingCandidates`**: 核心功能状态。成功获取建议后，`InputViewModel` 进入此状态，UI 会渲染候选列表，等待用户交互（选择、翻页等）。
-   **`Error`**: 异常状态。当网络或 API 出现问题时，`InputViewModel` 进入此状态，UI 会显示明确的错误信息，并提供清晰的退出路径（如按 `Esc`）。

通过这个由 `InputViewModel` 驱动的状态机，我们将复杂的异步交互流程（用户输入 -> 网络请求 -> UI 更新）转化为一系列定义清晰的状态转换，极大地降低了代码的复杂性，并提升了系统的健壮性。

## 5. 核心业务与性能指标

为了衡量 Siflowtype 的成功并指导未来的开发方向，我们定义以下关键指标：

### 5.1. 核心业务指标 (Business Metrics)

业务指标直接关联 Siflowtype 为用户创造的核心价值和产品的成功与否。

1.  **输入效率提升率**: 这是衡量产品核心价值的根本指标。可以通过对比用户在开启 `.ai` 模式与 `.english` 模式下，完成相同文本输入的平均时长或按键次数来量化。
2.  **用户采纳率与活跃度**:
    *   **AI模式使用率**: `.ai` 模式的日/周活跃用户比例，以及用户在两种模式下的使用时长分布。这反映了用户对AI辅助功能的依赖和认可程度。
    *   **功能留存率**: 首次使用AI功能的用户，在7日、30日后是否仍在继续使用。
3.  **用户满意度**:
    *   **基础功能满意度**: 通过用户反馈、评分等方式，衡量用户对 `.english` 模式稳定性和可靠性的满意度。根据文档中的“核心设计原则”，这是决定用户去留的基石。
    *   **AI建议满意度**: 对AI生成建议的准确性、相关性和及时性的评价。

### 5.2. 核心性能指标 (Performance Metrics)

性能指标是保障用户体验和业务指标实现的技术基础。

1.  **AI 建议延迟 (P95 Latency)**: 从用户完成输入到AI候选词出现在界面上的端到端时间（取95分位值）。这是最重要的性能指标，直接影响AI模式的可用性。建议设定目标值，如 `< 500ms`。
2.  **纯英文模式输入延迟**: 在 `.english` 模式下，从物理按键到字符上屏的延迟。此延迟必须做到用户无感知，与系统原生输入体验持平。
3.  **模式切换响应时间**: 用户按下快捷键（如 `Shift`）后，完成模式逻辑切换及UI提示（“AI”/“A”方块）出现所需的时间。应确保其即时响应，如 `< 100ms`。
4.  **API 调用成功率**: `OpenAIService` 的API请求成功率。目标应为 > 99.9%，任何低于此标准的情况都可能严重影响AI功能。
5.  **资源占用 (CPU & Memory)**:
    *   **空闲状态**: 应用在后台静默时的资源占用。
    *   **峰值状态**: 在高频输入和请求AI建议时的CPU和内存峰值。必须控制在合理范围内，避免影响系统其他应用的性能。