
# 模块设计文档: [[01_UI&UX设计规范]]

**版本**: 1.1
**最后更新**: 2025-07-22
**最近变更类型**: Feature

---

## 1. 核心职责与边界

- **核心职责**: 本文档定义了 Siflowtype 应用的统一视觉风格和交互行为，是所有 UI 组件设计的核心依据。它旨在确保产品界面的一致性、易用性和品牌识别度。
- **不做**: 本文档不涉及具体功能的实现逻辑，只关注于视觉表现和用户体验。

## 2. 功能点与用户旅程

### 2.1. 核心设计原则

1.  **无缝集成 (Seamless Integration):** 作为一款输入法，它必须无感地融入用户已有的工作流。设计上避免任何打扰性元素，仅在需要时出现。
2.  **即时响应 (Instantaneous Feedback):** 从输入到获得建议的延迟必须降到最低。UI 需要清晰地展示处理状态和结果，让用户有掌控感。
3.  **简洁直观 (Simplicity and Intuitiveness):** `[[候选词面板]]` 的设计应类似于系统自带的输入法，降低用户的学习成本。操作应符合直觉，如使用方向键和数字键选择。

### 2.2. 核心用户流程

核心交互流程是一个闭环：输入 -> 智能处理 -> 选择 -> 完成。

```mermaid
graph TD
    A["用户在任意应用中<br/>(如 Cursor) 输入想法碎片"] --> B{"Siflowtype 截获输入"};
    B --> C["调用 [[AI服务调用]]"];
    C --> D["AI 返回润色后的<br/>英文句子"];
    D --> E["在 [[候选词面板]]<br/>展示多个选项"];
    E --> F{"用户通过方向键或数字<br/>选择最合适的句子"};
    F --> G["将所选句子<br/>替换到原应用中"];

    subgraph "Siflowtype 核心交互循环"
        B
        C
        D
        E
        F
        G
    end
```

### 2.3. UI组件详细规范

#### 2.3.1. Siflowtype 候选框设计示意图

这张图展示了从用户输入到 AI 处理的两个关键视觉状态。

```mermaid
graph TD
    subgraph "状态一: 等待触发 / 显示结果"
        A["
            <div style='text-align: left; padding: 5px; font-family: sans-serif;'>
                <span style='background-color: #0A84FF; color: white; border-radius: 5px; padding: 5px 8px; display: inline-block;'>
                    <b>1 How are you doing?</b>
                    <br/>
                    <small>你最近怎么样？</small>
                </span>
                <span style='margin-left: 15px; display: inline-block;'>
                    <b>2 What's up?</b>
                    <br/>
                    <small>最近怎么样？</small>
                </span>
                <span style='position: absolute; right: 12px; top: 20px; font-size: 18px;'>
                    <b>⚿</b>
                </span>
            </div>
        "]
    end

    subgraph "状态二: AI 加载中"
        B["
            <div style='text-align: left; padding: 5px; font-family: sans-serif;'>
                <i>Thinking...</i>
                <span style='position: absolute; right: 10px; top: 15px; font-size: 18px;'>
                    <b>○</b>
                </span>
            </div>
        "]
    end

    A -- 按下 Tab 键 --> B;
    B -- AI 返回结果 --> A;
```

*（注意：上图为 HTML/CSS 模拟示意，颜色和图标仅为展示概念，加载状态的 ○ 代表旋转的圆环。实际开发将使用系统原生组件和颜色值。）*

#### 2.3.2. UI 组件纯文本描述

**组件：候选面板 (Candidate Panel)**

*   **容器 (Container)**
    *   **类型:** 悬浮面板 (Popover/Floating Panel)
    *   **形状:** 水平方向的圆角矩形。
    *   **背景:**
        *   **浅色模式:** 半透明的窗口背景材质 (`NSVisualEffectView.Material.popover`)。
        *   **深色模式:** 自动适应的对应暗色材质。
    *   **圆角:** 与系统标准 UI 控件一致。
    *   **阴影:** 轻微的、标准的系统窗口阴影。
*   **状态与内容 (States & Content)**
    *   **状态一: 显示结果 (Default/Displaying Results)**
        *   **布局:** 水平排列多个候选选项。
        *   **单个选项的格式:**
            *   **布局:** 两行垂直对齐。
            *   **第一行 (主文本):** 显示润色后的英文句子，前缀为数字 (e.g., "1 How are you doing?")。
                *   **字体:** 14pt, Regular, San Francisco (系统 UI 字体)。
                *   **颜色:** 标准标签色 (`NSColor.labelColor`)。
            *   **第二行 (次要文本):** 显示对应的中文翻译。
                *   **字体:** 12pt, Regular, San Francisco (系统 UI 字体)。
                *   **颜色:** 二级标签色 (`NSColor.secondaryLabelColor`)。
        *   **选中/高亮项:**
            *   **背景:** 该选项的背景被系统强调色 (`NSColor.controlAccentColor`) 的圆角矩形填充。
            *   **文本颜色:** 该选项内的所有文本颜色反转为高对比度颜色 (通常为白色)。
        *   **尾部图标:**
            *   **图标:** 静态的网格图标 (`square.grid.2x2` from SF Symbols)。
    *   **状态二: AI 加载中 (Loading)**
        *   **内容:** 面板内清除所有候选选项，显示文本 "Thinking..."。
            *   **字体:** 14pt, Italic, San Francisco。
            *   **颜色:** 二级标签色 (`NSColor.secondaryLabelColor`)。
        *   **尾部图标:**
            *   **图标:** 显示一个旋转的圆环形加载动画 (spinning ring)。
    *   **状态三: 错误 (Error)**
        *   **内容:** 面板显示一个特殊的错误提示项，采用上下两行格式，并带有区别于普通候选项的背景色。
            *   **第一行:** "Request failed. Please try again."
            *   **第二行:** "请求失败，请重试。"
        *   **背景:** 使用系统警告色（如橙色）的浅色版本作为背景，以提供视觉提示。
        *   **尾部图标:**
            *   **图标:** 静态的警告图标 (`exclamationmark.triangle.fill` from SF Symbols)。
            *   **颜色:** 系统警告色 (`NSColor.systemOrange`)。
        *   **交互逻辑:**
            *   **按 `Esc` 键:** 关闭候选面板，用户的原始输入保持不变。
            *   **按 `Backspace` 键:** 一键删除用户的全部原始输入，并关闭候选面板。

#### 2.3.3. 设计规范细节

##### 3.1 整体原则
*   **一致性:** 严格遵循 Apple 的 [Human Interface Guidelines (HIG)](https://developer.apple.com/design/human-interface-guidelines/)，确保组件外观和行为与原生 macOS 应用无异。
*   **自适应:** 所有颜色、字体和间距都应能自动适应系统的浅色模式 (Light Mode) 和深色模式 (Dark Mode)。

##### 3.2 颜色 (Color Palette)
*   **背景 (Background):**
    *   **规范:** 使用系统标准窗口背景色或略微透明的材质 (e.g., `NSVisualEffectView.Material.popover`)。
    *   **实现建议:** `NSColor.windowBackgroundColor` 或 `NSVisualEffectView`。
*   **高亮/选中项背景 (Highlight):**
    *   **规范:** 使用系统当前设置的强调色 (Accent Color)。
    *   **实现建议:** `NSColor.controlAccentColor`。
*   **文本颜色 (Text):**
    *   **主文本 (英文):** 系统标准标签颜色。当被高亮时，颜色应自动反转为高对比度颜色（通常为白色）。
    *   **实现建议:** `NSColor.labelColor`。
    *   **次要文本 (中文翻译):** 系统二级标签颜色，用于提供视觉上的次要感。
    *   **实现建议:** `NSColor.secondaryLabelColor`。

##### 3.3 字体 (Typography)
*   **字体家族:** 默认使用系统 UI 字体 (San Francisco)。
*   **主文本 (英文):**
    *   **字号:** 14pt (或 `NSFont.TextStyle.body`)。
    *   **字重:** Regular。
*   **次要文本 (中文):**
    *   **字号:** 12pt (或 `NSFont.TextStyle.subheadline`)。
    *   **字重:** Regular。

##### 3.4 布局与间距 (Layout & Spacing)
*   **面板圆角:** 与系统标准控件（如搜索框、按钮）的圆角半径保持一致。
*   **内部边距 (Padding):** 建议在面板的上下左右留出 `8pt` 的内边距。
*   **项目间距:** 各个候选词之间建议有 `12pt` 的水平间距。
*   **行间距:** 英文主文本与下方中文翻译的垂直间距建议为 `2pt` 到 `4pt`。

##### 3.5 尾部图标与状态动画 (Trailing Icon & Animations)
尾部的图标是用户感知系统状态的关键窗口。

*   **默认/空闲状态:**
    *   **图标:** 显示一个静态的网格图标，暗示可进行设置或切换模式。
    *   **实现建议:** 使用 SF Symbols 中的 `square.grid.2x2`。
*   **加载状态:**
    *   **图标:** 网格图标应被替换为一个表示"加载中"的动画指示器。
    *   **动画:**
        *   **方案A (推荐):** 实现一个自定义的圆环形加载动画 (类似 Apple Watch 的加载圈)。动画应为一个不断追逐自己尾巴的圆弧，以提供比系统默认"菊花"更现代、更精致的感觉。
        *   **方案B (备选):** 如果自定义动画实现复杂，可回退到使用系统标准的圆形进度指示器 (`NSProgressIndicator`)，但需将其样式设置为 `spinning` 而非 `bar`。
*   **错误状态:**
    *   **图标:** 如果 AI 服务调用失败，加载动画停止，图标变为警告标志。
    *   **实现建议:** 使用 SF Symbols 中的 `exclamationmark.triangle.fill`，颜色使用 `NSColor.systemOrange`。
    *   **提示背景:** 为错误信息添加一个背景色，建议使用 `NSColor.systemOrange.withAlphaComponent(0.2)`，使其在视觉上与其他状态区分开，同时避免过度干扰。
    *   **交互:** 相关的 `Esc` 和 `Backspace` 快捷键逻辑需严格按照用户故事中的验收标准实现。

## 3. 数据模型

本模块为纯设计规范，不包含数据模型。

## 4. API 接口

本模块为纯设计规范，不包含 API 接口设计。

## 5. 关联模块

### 5.1. 依赖于 (Depends On)

- `[[02_产品需求]]`

### 5.2. 被依赖于 (Depended On By)

- `[[候选词面板]]`

## 6. 变更历史 (Change History)

- **v1.1 (2025-07-22, Feature):** 新增了详细的UI组件规范、设计示意图和状态定义。
- **v1.0 (2025-07-22, Initial):** 基于 `README.md` 的内容创建初始版本。