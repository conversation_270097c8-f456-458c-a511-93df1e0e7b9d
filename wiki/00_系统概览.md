# 系统概览: Siflowtype

**版本**: 1.0
**最后更新**: 2025-07-22

---

## 1. 项目核心价值

Siflowtype 是一款专为开发者设计的 AI 英文输入法，旨在消除非英语母语者在与 AI 编程助手进行英文对话时的表达障碍。它通过将用户输入的碎片化想法实时转化为语法正确、表达地道的高质量英文，来提升沟通效率和开发体验。

## 2. 建议的模块列表

基于对项目 `README.md` 的分析，建议将系统划分为以下几个核心模块：

- **`[[用户输入处理]]`**: 
  - **核心职责**: 负责从操作系统层面截获用户的键盘输入，并根据预设规则（如按下 `Tab` 键）决定何时触发 AI 润色流程。

- **`[[AI服务调用]]`**: 
  - **核心职责**: 封装与第三方 AI 服务（如 OpenAI API）的所有交互。负责发送用户输入、处理认证、解析返回的建议，并处理可能发生的网络错误。

- **`[[候选词面板]]`**: 
  - **核心职责**: 系统的核心 UI 组件。负责以悬浮面板的形式展示 AI 返回的多个候选句子。管理不同状态下的显示逻辑（加载中、显示结果、错误提示），并处理用户的选择操作（方向键、数字键）。

- **`[[核心交互逻辑]]`**: 
  - **核心职责**: 作为系统的“大脑”，编排其他模块协同工作。它监听 `[[用户输入处理]]` 模块的触发信号，调用 `[[AI服务调用]]` 模块获取数据，然后将数据传递给 `[[候选词面板]]` 进行展示，最后将用户的选择结果写回目标应用程序。

- **`[[设置与状态管理]]`**: 
  - **核心职责**: 提供用户配置界面（如果需要），并管理应用的全局状态，例如 Siflowtype 的启用/禁用状态。处理快捷键（如 `Option + Space`）以切换状态，并提供相应的视觉反馈。

- **`[[用户认证]]`**:
  - **核心职责**: 负责通过 `helios-api` 的 OAuth 流程对用户进行身份验证，并管理用户的认证令牌，为后续的订阅支付等功能提供支持。

- **`[[客户端OAuth实现方案]]`**:
  - **核心职责**: 提供了在 macOS 客户端上实现 OAuth 2.0 授权的具体技术方案和代码指南。

## 3. 初步模块依赖关系图

```mermaid
graph TD
    subgraph "外部依赖"
        Helios["helios-api"]
    end

    subgraph "核心功能"
        Input["[[用户输入处理]]"]
        Logic["[[核心交互逻辑]]"]
        AI["[[AI服务调用]]"]
        Panel["[[候选词面板]]"]
        Settings["[[设置与状态管理]]"]
        Auth["[[用户认证]]"]
    end

    subgraph "未来规划"
        Payment["[[订阅支付]]"]
    end

    User["用户"] -- "输入" --> Input
    User -- "查看/选择" --> Panel
    User -- "登录" --> Auth

    Input --> Logic
    Logic -- "触发" --> AI
    AI -- "结果" --> Logic
    Logic -- "更新" --> Panel
    Settings -- "配置" --> Logic
    
    Auth -- "认证信息" --> Logic
    Auth -- "依赖" --> Helios
    Payment -- "依赖" --> Auth
```