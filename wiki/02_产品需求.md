# 模块设计文档: [[产品需求]]

**版本**: 1.0
**最后更新**: 2025-07-22
**最近变更类型**: Initial

---

## 1. 核心职责与边界

- **核心职责**: 本文档是 Siflowtype 产品的**唯一需求来源 (Single Source of Truth)**。它详细定义了产品的愿景、目标、用户画像、用户故事和关键功能，为后续的系统设计、开发和测试提供明确的指导和依据。
- **不做**: 
  - 本文档不包含具体的技术实现细节、API设计或数据模型。这些内容应在各自对应的技术设计模块中定义。
  - 本文档不作为项目的任务跟踪工具。具体的开发任务应在项目管理工具中进行。

## 2. 功能点与用户旅程

功能点和用户旅程通过用户故事的形式进行定义。

### 2.1. 用户故事 (User Stories)

| 优先级 | 作为一个... (As a...) | 我想要... (I want to...) | 以便... (So that...) | 验收标准 (Acceptance Criteria) |
| :--- | :--- | :--- | :--- | :--- |
| **P0** | 开发者 | 输入碎片化的中英文、术语、甚至代码片段 | Siflowtype 能实时生成完整、通顺的英文句子。 | 1. **触发方式:** 用户输入文本后，按下 `Tab` 键，应触发 `[[AI服务调用]]`。<br>2. **数据要求:** AI 服务需同时返回润色后的英文句子和对应的中文翻译。<br>3. **显示格式:** `[[候选词面板]]` 中，每个英文句子下方应以稍小的灰色字体显示其中文翻译。<br>4. **状态显示:** 触发后，`[[候选词面板]]` 应立即显示加载状态。<br>5. **行为约束:** 在 AI 结果返回前，重复按 `Tab` 键不应发起新的请求。 |
| **P0** | 用户 | 使用上下方向键和数字键来选择候选词 | 这符合我使用传统输入法的习惯，无需学习。 | 1. `↓` 键使焦点下移，`↑` 键使焦点上移。<br>2. 按下数字 `1-N` 键，直接选中对应的候选词并替换输入。 |
| **P1** | 用户 | 通过一个简单的快捷键（如 `Option + Space`）来快速启用/禁用 Siflowtype | 我可以在需要时激活它，不需要时它不会打扰我。 | 1. 按下快捷键后，Siflowtype 的功能开关状态反转。<br>2. 应有视觉提示（如菜单栏图标变化）来表明当前状态。该功能由 `[[设置与状态管理]]` 模块负责。 |
| **P1** | 用户 | 看到一个清晰的加载指示器 | 当 AI 正在处理时，我知道系统没有卡住。 | 1. 调用 `[[AI服务调用]]` 时，`[[候选词面板]]` 内应有类似 `...` 或 `Thinking...` 的文本或动画。 |
| **P1** | 用户 | 从 AI 的错误或无效返回中快速恢复 | 我不会被卡住，可以无缝地重新尝试输入。 | 1. **错误显示:** 当 `[[AI服务调用]]` 返回错误时，`[[候选词面板]]` 应显示一个带警告背景色的、内容为"Request failed. Please try again. / 请求失败，请重试。"的提示。<br>2. **`Esc` 键:** 在错误状态下，按 `Esc` 键应直接关闭 `[[候选词面板]]`，保留用户原始输入。<br>3. **`Backspace` 键:** 在错误状态下，按 `Backspace` 键应一键删除用户的全部原始输入，并关闭 `[[候选词面板]]`，让用户可以立即重新输入。 |
| **P2** | 高级用户 | 自定义触发 AI 润色的方式（例如，输入完成后按 `Tab` 键） | 我可以根据自己的习惯配置最高效的工作流。 | 1. 在 `[[设置与状态管理]]` 模块提供的设置界面中，允许用户修改触发键。 |

## 3. 数据模型

本模块为纯需求定义，不包含数据模型。

## 4. API 接口

本模块为纯需求定义，不包含 API 接口设计。

## 5. 关联模块

### 5.1. 依赖于 (Depends On)

- 无

### 5.2. 被依赖于 (Depended On By)

- `[[系统概览]]`
- `[[用户输入处理]]`
- `[[AI服务调用]]`
- `[[候选词面板]]`
- `[[核心交互逻辑]]`
- `[[设置与状态管理]]`

## 6. 变更历史 (Change History)

- **v1.0 (2025-07-22, Initial):** 基于 `README.md` 的内容创建初始版本。