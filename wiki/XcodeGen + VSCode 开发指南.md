# XcodeGen + VSCode 开发工作流指南

本文档旨在为使用 XcodeGen 和 Visual Studio Code (VSCode) 进行 macOS 应用开发的团队提供一套标准化的工作流程。遵循本指南可以确保项目结构的一致性、依赖管理的便捷性以及开发环境的统一性。

> https://docs.trae.ai/ide/manage-extensions#4ca6a524
> https://marketplace.visualstudio.com/items?itemName=swiftlang.swift-vscode
> https://marketplace.visualstudio.com/items?itemName=llvm-vs-code-extensions.lldb-dap

## 核心理念

- **`project.yml` 作为唯一信源**: 项目的所有配置，包括文件结构、依赖关系、构建设置等，都必须在 `project.yml` 文件中定义。
- **自动化项目生成**: 通过 `xcodegen` 命令自动生成 `.xcodeproj` 文件，避免手动管理 Xcode 项目文件带来的冲突和不一致。
- **IDE 灵活性**: 开发者可以在 VSCode 中进行高效的编码，同时利用 Xcode 进行编译、调试和性能分析。

## 关键步骤

### 1. 配置 `project.yml`

`project.yml` 是整个项目的核心配置文件。以下是一些关键的配置项说明。

#### 1.1. 添加 Swift Package Manager (SPM) 依赖

要添加一个远程的 SPM 依赖，你需要在 `project.yml` 中定义 `packages` 和 `dependencies` 部分。

- **`packages`**: 在这里声明项目所需的所有 SPM 包及其来源。
- **`dependencies`**: 在具体的 `target` 中引用在 `packages` 中声明的包。

**示例：添加 OpenAI 包**

```yaml
name: Siflowtype
options:
  bundleIdPrefix: com.siflow

packages:
  OpenAI:
    url: https://github.com/MacPaw/OpenAI.git
    from: 1.0.0 # 或者使用 branch: main, exact: 1.0.0 等

targets:
  Siflowtype:
    type: application
    platform: macOS
    sources: [Application, Features, Services, Utilities]
    dependencies:
      - package: OpenAI
```

#### 1.2. 配置自动代码签名

为了简化签名流程，推荐使用 Xcode 的自动管理签名功能。这可以通过在 `project.yml` 中设置特定的构建参数来实现。

- **`CODE_SIGN_STYLE: Automatic`**: 启用自动签名。
- **`DEVELOPMENT_TEAM`**: 指定你的 Apple Developer Team ID。

**示例：配置自动签名**

```yaml
targets:
  Siflowtype:
    type: application
    platform: macOS
    settings:
      base:
        CODE_SIGN_STYLE: Automatic
        DEVELOPMENT_TEAM: YOUR_TEAM_ID # <--- 在这里替换成你的 Team ID
```

##### 如何找到你的 `DEVELOPMENT_TEAM` ID？

1.  登录到 [Apple Developer 网站](https://developer.apple.com/account/)。
2.  进入 **Membership** (会员资格) 页面。
3.  **Team ID** 就显示在你的会员信息中。

### 2. 生成 Xcode 项目

每当 `project.yml` 文件或项目的文件结构（例如，添加或删除文件/文件夹）发生变化时，你都必须重新生成 `.xcodeproj` 文件以保持同步。这对于确保 VSCode 的 Swift 语言服务器 (SourceKit-LSP) 能够正确识别项目结构和依赖至关重要。

在项目根目录下运行以下命令：

```bash
xcodegen generate
```

此命令会读取 `project.yml` 的内容，并重新生成或更新 `Siflowtype.xcodeproj` 文件。

### 3. 日常开发工作流

1.  **修改代码**: 在 VSCode 中打开项目，进行代码的编写和修改。
2.  **管理项目结构**: 
    - 如果需要添加或删除源文件，请直接在文件系统中创建或删除文件，然后更新 `project.yml` 中的 `sources` 列表。
    - 如果需要添加新的 SPM 依赖，请修改 `project.yml` 的 `packages` 和 `dependencies` 部分。
3.  **重新生成项目**: 在对 `project.yml` 或文件结构进行任何更改后，立即运行 `xcodegen generate`。
4.  **编译与调试**: 打开生成的 `.xcodeproj` 文件，使用 Xcode 进行编译、运行和调试。

遵循此工作流，可以最大限度地发挥 XcodeGen 和 VSCode 结合使用的优势，实现高效、可维护的开发过程。