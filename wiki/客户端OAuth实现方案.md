# 客户端 OAuth 实现方案 (macOS)

> **[!] 重要依赖警告**
> 根据 `helios-api` 的 `[[认证授权]]` 模块文档 v2.0，为原生桌面应用设计的 **OAuth 2.0 授权码 + PKCE** 流程目前处于 **“架构预留，待实现”** 状态。本客户端的设计与实现将基于该流程，因此强依赖于 `helios-api` 服务端完成此功能的开发。

**最后更新**: 2025-07-22

本文档为 Siflowtype macOS 客户端实现基于 `helios-api` 的 OAuth 2.0 授权流程提供详细的技术方案和代码示例。

---

## 1. 核心流程概览

客户端的 OAuth 流程遵循“授权码流程 (Authorization Code Flow)”并结合 PKCE (Proof Key for Code Exchange) 以增强安全性，这对于无法安全存储 `client_secret` 的原生应用至关重要。

1.  **准备阶段**: 客户端生成 `code_verifier` 和 `code_challenge`。
2.  **触发授权**: 用户点击登录，客户端自行构建 `helios-api` 的授权 URL (包含 PKCE 参数)，然后使用 `NSWorkspace` 在浏览器中打开该 URL。
3.  **用户授权**: 用户在浏览器中登录并授权。
4.  **回调处理**: `helios-api` 通过自定义 URL Scheme (Custom URL Scheme) 回调客户端，并附带 `authorization_code`。
5.  **令牌交换**: 客户端捕获回调，将 `authorization_code` 和 `code_verifier` 直接发送至 `helios-api` 的 `/api/v1/oauth-token` 端点。
6.  **完成认证**: `helios-api` 验证通过后，直接向客户端返回 `access_token` 和 `refresh_token`。
7.  **安全存储**: 客户端将获取到的 Tokens 安全地存储在 macOS 的钥匙串 (Keychain) 中。

## 2. 详细实现步骤

### 步骤 1: 配置 Custom URL Scheme

这是让浏览器能够回调我们应用的关键。

在 Xcode 项目的 `Info.plist` 文件中，添加以下配置：

```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>com.siflowtype.auth</string>  <!-- 建议使用反向域名表示唯一性 -->
        <key>CFBundleURLSchemes</key>
        <array>
            <string>siflowtype</string>      <!-- 这就是我们的 URL Scheme -->
        </array>
    </dict>
</array>
```

配置完成后，`siflowtype://auth/callback?code=...` 这样的 URL 就能唤起我们的应用。

### 步骤 2: 实现 PKCE

在发起授权前，我们需要生成 PKCE 所需的参数。

```swift
import CryptoKit
import Foundation

struct PKCE {
    let codeVerifier: String
    let codeChallenge: String

    init?() {
        // 1. 生成一个随机的 code_verifier
        var buffer = [UInt8](repeating: 0, count: 32)
        let status = SecRandomCopyBytes(kSecRandomDefault, buffer.count, &buffer)
        guard status == errSecSuccess else { return nil }
        self.codeVerifier = Data(buffer).base64URLEncodedString()

        // 2. 基于 verifier 生成 SHA256 哈希的 code_challenge
        guard let verifierData = codeVerifier.data(using: .utf8) else { return nil }
        let challengeData = SHA256.hash(data: verifierData)
        self.codeChallenge = Data(challengeData).base64URLEncodedString()
    }
}

extension Data {
    func base64URLEncodedString() -> String {
        return self.base64EncodedString()
            .replacingOccurrences(of: "+", with: "-")
            .replacingOccurrences(of: "/", with: "_")
            .replacingOccurrences(of: "=", with: "")
    }
}
```

### 步骤 3: 启动授权流程

当用户点击登录时，执行此操作。

```swift
import AppKit

class AuthService {
    var pkce: PKCE?

    func startLogin() {
        // 1. 生成并保存 PKCE 对
        self.pkce = PKCE()
        guard let pkce = self.pkce else {
            // 处理错误：无法生成 PKCE
            return
        }

        // 2. 客户端自行构建授权 URL
        // 注意：此处的 `authorize` 端点在 helios-api 中尚待实现
        var components = URLComponents(string: "https://api.helios.com/api/v1/oauth/authorize")!
        components.queryItems = [
            URLQueryItem(name: "response_type", value: "code"),
            URLQueryItem(name: "client_id", value: "your_client_id_for_siflowtype"),
            URLQueryItem(name: "redirect_uri", value: "siflowtype://auth/callback"),
            URLQueryItem(name: "scope", value: "user:profile payment:subscribe"),
            URLQueryItem(name: "state", value: UUID().uuidString), // 使用随机字符串作为 state
            URLQueryItem(name: "code_challenge", value: pkce.codeChallenge),
            URLQueryItem(name: "code_challenge_method", value: "S256"),
        ]

        guard let url = components.url else {
            // 处理错误：无法构建 URL
            return
        }

        // 3. 使用默认浏览器打开 URL
        NSWorkspace.shared.open(url)
    }\n    // ... 其他方法
}
```

### 步骤 4: 监听和处理 URL Scheme 回调

在 `AppDelegate` 或你的 App 生命周期管理类中，监听 URL 事件。

```swift
// AppDelegate.swift

func applicationDidFinishLaunching(_ aNotification: Notification) {
    NSAppleEventManager.shared().setEventHandler(
        self,
        andSelector: #selector(handleGetURL(event:withReplyEvent:)),
        forEventClass: kInternetEventClass,
        andEventID: kAEGetURL
    )
}

@objc func handleGetURL(event: NSAppleEventDescriptor!, withReplyEvent: NSAppleEventDescriptor!) {
    guard let urlString = event.paramDescriptor(forKeyword: keyDirectObject)?.stringValue,
          let url = URL(string: urlString) else {
        return
    }

    // 检查是否是我们的回调 URL
    guard url.scheme == "siflowtype", url.host == "auth", url.path == "/callback" else {
        return
    }

    // 解析授权码
    guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
          let code = components.queryItems?.first(where: { $0.name == "code" })?.value else {
        // 处理错误：回调中没有授权码
        return
    }

    // 使用授权码完成登录
    AuthService.shared.exchangeCodeForToken(code: code)
}
```

### 步骤 5: 交换令牌并存储

在 `AuthService` 中添加方法，将授权码直接发送到 `helios-api`。

```swift
// AuthService.swift (续)

func exchangeCodeForToken(code: String) {
    guard let codeVerifier = self.pkce?.codeVerifier else {
        // 处理错误：code_verifier 丢失
        return
    }

    // 这里应该使用一个通用的网络请求客户端来调用 helios-api
    APIClient.shared.exchangeToken(code: code, codeVerifier: codeVerifier) { result in
        switch result {
        case .success(let tokenResponse):
            // 6. 成功获取令牌，安全存储
            KeychainService.shared.save(token: tokenResponse.accessToken, for: "accessToken")
            KeychainService.shared.save(token: tokenResponse.refreshToken, for: "refreshToken")
            
            // 7. 通知 UI 登录成功，更新界面
            NotificationCenter.default.post(name: .didLoginSuccessfully, object: nil)

        case .failure(let error):
            // 处理错误：令牌交换失败
            print("Error exchanging token: \(error)")
        }
    }
}
```

### 步骤 6: 使用钥匙串 (Keychain) 安全存储

使用 Keychain 是在 Apple 平台上存储敏感数据的标准做法。

```swift
import Foundation
import Security

class KeychainService {
    static let shared = KeychainService()
    private let service = "com.siflowtype.auth"

    func save(token: String, for key: String) -> Bool {
        guard let data = token.data(using: .utf8) else { return false }

        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data
        ]

        // 先删除旧的，避免重复
        SecItemDelete(query as CFDictionary)

        let status = SecItemAdd(query as CFDictionary, nil)
        return status == errSecSuccess
    }

    func retrieve(for key: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: kCFBooleanTrue!,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]

        var dataTypeRef: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &dataTypeRef)

        if status == errSecSuccess {
            if let retrievedData = dataTypeRef as? Data {
                return String(data: retrievedData, encoding: .utf8)
            }
        }
        return nil
    }
    
    func delete(for key: String) {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key
        ]
        SecItemDelete(query as CFDictionary)
    }
}
```

## 3. UI 实现建议

### 菜单栏状态管理

- 使用一个 `StateObject` 或 `ObservableObject` 来管理用户的登录状态。
- `MenuBarExtra` (SwiftUI) 的内容根据这个状态动态切换。

```swift
// SiflowtypeApp.swift

import SwiftUI

@main
struct SiflowtypeApp: App {
    @StateObject private var authManager = AuthenticationManager()

    var body: some Scene {
        MenuBarExtra {
            if authManager.isLoggedIn {
                UserInfoView()
                    .environmentObject(authManager)
            } else {
                LoginView()
                    .environmentObject(authManager)
            }
        } label: {
            Image(systemName: authManager.isLoggedIn ? "person.crop.circle.fill" : "person.crop.circle")
        }
    }
}

class AuthenticationManager: ObservableObject {
    @Published var isLoggedIn: Bool = false
    // ... 登录、登出、检查初始状态的逻辑
}

struct LoginView: View {
    @EnvironmentObject var authManager: AuthenticationManager
    var body: some View {
        Button("登录") {
            AuthService.shared.startLogin()
        }
    }
}

struct UserInfoView: View {
    @EnvironmentObject var authManager: AuthenticationManager
    // ... 显示用户信息和登出按钮
}
```

### 监听登录状态变化

在 `AuthenticationManager` 中，监听我们之前定义的 `didLoginSuccessfully` 通知，以在令牌交换成功后自动更新 UI。

```swift
// AuthenticationManager.swift (续)

init() {
    // 检查启动时钥匙串中是否已有令牌
    self.isLoggedIn = KeychainService.shared.retrieve(for: "accessToken") != nil

    // 监听登录成功通知
    NotificationCenter.default.addObserver(forName: .didLoginSuccessfully, object: nil, queue: .main) { _ in
        self.isLoggedIn = true
    }
    
    // 监听登出通知
    // ...
}
```

通过以上步骤，即可在 Siflowtype 中构建一个安全、健壮且用户体验良好的 OAuth 登录系统。