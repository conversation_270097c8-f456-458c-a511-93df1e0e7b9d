# 模块设计文档: [[用户测试用例]]

**版本**: 1.0
**最后更新**: 2024-07-23
**最近变更类型**: Initial

---

## 1. 概述

本文档基于 `[[产品需求]]` 文档，从终端用户的实际使用视角出发，定义了一系列测试用例，旨在验证 Siflowtype 的功能完整性、稳定性和用户体验。测试覆盖了核心功能、交互流程、边界条件和异常处理。

## 2. 测试用例

### 2.1. 核心功能测试 (P0)

| 用例ID | 测试标题 | 优先级 | 前置条件 | 测试步骤 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-CORE-001** | **成功触发AI润色并显示候选词** | P0 | 1. Siflowtype 已启用。<br>2. 网络连接正常。 | 1. 在任意文本输入框中，输入 `dev`。<br>2. 按下 `Tab` 键。 | 1. `[[候选词面板]]` 立即出现，并显示加载状态（如 `...`）。<br>2. 短暂延迟后，面板中显示多个润色后的英文句子（如 `developer`）。<br>3. 每个英文句子下方，以灰色小字体显示对应的中文翻译。 |
| **TC-CORE-002** | **使用方向键选择候选词** | P0 | 1. 已执行 `TC-CORE-001`，候选词面板可见。 | 1. 按下 `↓` 键。<br>2. 按下 `↓` 键。<br>3. 按下 `↑` 键。 | 1. 面板中第二个候选词被高亮。<br>2. 第三个候选词被高亮。<br>3. 第二个候选词再次被高亮。 |
| **TC-CORE-003** | **使用数字键选择并替换文本** | P0 | 1. 已执行 `TC-CORE-001`，候选词面板可见。 | 1. 按下数字键 `1`。 | 1. 候选词面板消失。<br>2. 输入框中的 `dev` 被替换为第一个候选词的内容。 |
| **TC-CORE-004** | **AI请求期间阻止重复触发** | P0 | 1. Siflowtype 已启用。<br>2. 网络状况较差，AI响应慢。 | 1. 输入 `long query`。<br>2. 快速连续按下 `Tab` 键 3 次。 | 1. 只有第一次按下 `Tab` 键会触发 `[[AI服务调用]]`。<br>2. 候选词面板显示加载状态，且在结果返回前，后续的 `Tab` 按键无效。<br>3. 网络请求只被发送一次。 |

### 2.2. 交互与状态测试 (P1)

| 用例ID | 测试标题 | 优先级 | 前置条件 | 测试步骤 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-INT-001** | **使用快捷键启用/禁用应用** | P1 | 无 | 1. 按下快捷键 `Option + Space`。<br>2. 输入文本并按 `Tab`。<br>3. 再次按下 `Option + Space`。<br>4. 输入文本并按 `Tab`。 | 1. 菜单栏图标状态改变，表示应用已禁用。<br>2. 按 `Tab` 无任何反应。<br>3. 菜单栏图标恢复，表示应用已启用。<br>4. 按 `Tab` 正常触发AI润色。 |
| **TC-INT-002** | **使用 `Esc` 键关闭候选词面板** | P1 | 1. 已执行 `TC-CORE-001`，候选词面板可见。 | 1. 按下 `Esc` 键。 | 1. 候选词面板消失。<br>2. 用户原始输入（`dev`）被保留在输入框中。 |
| **TC-INT-003** | **AI服务返回错误时显示提示** | P1 | 1. Siflowtype 已启用。<br>2. 模拟 `[[AI服务调用]]` 返回一个服务器错误。 | 1. 输入 `test error`。<br>2. 按下 `Tab` 键。 | 1. 候选词面板出现，并显示明确的错误提示信息（如 `Request failed. Please try again. / 请求失败，请重试。`），且背景色为警告色。 |
| **TC-INT-004** | **从错误状态通过 `Esc` 恢复** | P1 | 1. 已执行 `TC-INT-003`，面板处于错误状态。 | 1. 按下 `Esc` 键。 | 1. 候选词面板消失。<br>2. 用户的原始输入 (`test error`) 被保留。 |
| **TC-INT-005** | **从错误状态通过 `Backspace` 恢复** | P1 | 1. 已执行 `TC-INT-003`，面板处于错误状态。 | 1. 按下 `Backspace` 键。 | 1. 候选词面板消失。<br>2. 用户的原始输入 (`test error`) 被完全删除。 |

### 2.3. 边界与异常测试

| 用例ID | 测试标题 | 优先级 | 前置条件 | 测试步骤 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-EDGE-001** | **输入为空时触发** | P1 | 1. Siflowtype 已启用。 | 1. 在输入框为空的情况下，按下 `Tab` 键。 | 1. 不应触发任何操作，候选词面板不出现。 |
| **TC-EDGE-002** | **输入包含特殊字符或代码** | P0 | 1. Siflowtype 已启用。 | 1. 输入 `const a = 1;`。<br>2. 按下 `Tab` 键。 | 1. AI 服务能正确处理并返回合理的润色建议。 |
| **TC-EDGE-003** | **无网络连接** | P1 | 1. Siflowtype 已启用。<br>2. 断开电脑网络连接。 | 1. 输入 `test offline`。<br>2. 按下 `Tab` 键。 | 1. 候选词面板应立即或在短暂超时后显示网络错误相关的提示。 |
| **TC-EDGE-004** | **AI返回空结果或无效格式** | P1 | 1. Siflowtype 已启用。<br>2. 模拟 `[[AI服务调用]]` 返回空数组或非预期的JSON结构。 | 1. 输入 `test empty`。<br>2. 按下 `Tab` 键。 | 1. 候选词面板应显示一个友好的错误提示，告知用户未找到建议或服务暂时不可用。 |

### 2.4. 配置功能测试 (P2)

| 用例ID | 测试标题 | 优先级 | 前置条件 | 测试步骤 | 预期结果 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **TC-CONF-001** | **自定义触发键** | P2 | 1. Siflowtype 已启用。 | 1. 进入 `[[设置与状态管理]]` 提供的设置界面。<br>2. 将触发键从 `Tab` 修改为 `Enter`。<br>3. 保存设置。<br>4. 输入 `test enter`，按下 `Tab`。<br>5. 输入 `test enter`，按下 `Enter`。 | 1. 设置能成功保存。<br>2. 按下 `Tab` 键无反应。<br>3. 按下 `Enter` 键成功触发AI润色。 |

## 3. 关联模块

- `[[产品需求]]`