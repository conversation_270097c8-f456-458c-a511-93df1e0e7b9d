# 模块设计文档: [[用户认证]]

> **[!] 重要依赖警告**
> 根据 `helios-api` 的 `[[认证授权]]` 模块文档 v2.0，为原生桌面应用设计的 **OAuth 2.0 授权码 + PKCE** 流程目前处于 **“架构预留，待实现”** 状态。本客户端的设计与实现将基于该流程，因此强依赖于 `helios-api` 服务端完成此功能的开发。

**版本**: 1.0
**最后更新**: 2025-07-22
**最近变更类型**: Initial

---

## 1. 核心职责与边界

- **核心职责**: 
  - 管理用户的身份验证流程，支持通过外部 `helios-api` 的 OAuth2.0 协议进行登录。
  - 安全地存储和管理用户的访问令牌 (Access Token) 和刷新令牌 (Refresh Token)。
  - 向应用内其他模块提供当前用户的认证状态和基本信息。
- **不做**: 
  - 不负责用户密码的存储和管理（完全委托给 `helios-api`）。
  - 不处理支付和订阅逻辑，仅传递认证凭据给相关模块。

## 2. 功能点与用户旅程

- **F1: 用户通过 OAuth 登录**
  - **描述**: 用户在未登录状态下，点击菜单栏的“登录”按钮。应用将调用系统默认浏览器打开 `helios-api` 的 OAuth2 授权页面。用户在浏览器中完成授权后，`helios-api` 将通过预设的 Custom URL Scheme 回调客户端。客户端获取授权码 (Authorization Code)后，将其发送到我方服务端进行验证和换取令牌。
  - **依赖**: `[[helios-api]]` (外部服务), `[[核心交互逻辑]]` (触发浏览器跳转)
  - **依赖**: `[[helios-api]]` (外部服务)

- **F2: 使用 Refresh Token 刷新认证**
  - **描述**: 当 Access Token 过期时，系统在后台自动使用 Refresh Token 向 `helios-api` 请求新的 Access Token，对用户无感。
  - **依赖**: `[[helios-api]]` (外部服务)

- **F3: 用户登出**
  - **描述**: 用户在个人信息面板中点击“退出登录”按钮，客户端将调用服务端接口完成登出，并清除本地存储的用户认证信息（Tokens）。

- **F4: 显示用户信息**
  - **描述**: 用户在已登录状态下，菜单栏显示其个人信息（如头像或昵称）。点击后，弹出一个面板，展示用户的头像、昵称、订阅状态等基本信息，并提供“退出登录”按钮。
  - **依赖**: `[[UI模块]]` (需要一个新的用户信息面板)

## 3. 数据模型

### `UserAuthentication` (本地存储 / Keychain)
| 字段名 | 类型 | 约束/注释 |
| --- | --- | --- |
| userId | String | PK, 来自 `helios-api` 的用户唯一标识 |
| accessToken | String | 安全存储在 Keychain 中 |
| refreshToken | String | 安全存储在 Keychain 中 |
| expiresAt | Timestamp | Access Token 的过期时间 |

## 4. API 接口

> **注意**: 以下所有 API 均为 `helios-api` 服务端提供的标准 OAuth 2.0 接口。Siflowtype 客户端将直接调用这些接口。由于客户端是公开的，认证流程**必须**使用 PKCE (Proof Key for Code Exchange) 扩展来确保安全，替代 `client_secret` 的使用。

### `GET /api/v1/oauth/authorize` (待实现)
- **描述**: 重定向用户到 `helios-api` 的登录和授权页面。这是 OAuth 2.0 授权码流程的标准入口点。
- **注意**: 此端点在 `helios-api` 的 `[[认证授权]]` 文档中暂未定义，是客户端实现流程所 **必需** 的。客户端将使用此端点，并附带 `response_type=code`、`client_id`、`redirect_uri`、`code_challenge` 和 `code_challenge_method=S256` 等参数。

### `POST /api/v1/oauth-token` (helios-api)
- **描述**: 客户端在收到回调的授权码后，直接调用此接口，将其与 PKCE 的 `code_verifier` 一起发送给 `helios-api` 以换取令牌。
- **核心请求体**:
  - `grant_type=authorization_code`
  - `code`: 从回调中获取的授权码。
  - `redirect_uri`: 与授权时使用的 `redirect_uri` 必须一致。
  - `client_id`: Siflowtype 的客户端ID。
  - `code_verifier`: PKCE 的原始验证器。
- **响应**: `{ "access_token": "...", "refresh_token": "...", "expires_in": 3600, "token_type": "Bearer" }`

### `POST /api/v1/oauth/revoke` (建议)
- **描述**: 吊销一个 `access_token` 或 `refresh_token`。
- **注意**: 此端点在 `helios-api` 的 `[[认证授权]]` 文档中暂未定义，但对于实现安全的登出功能至关重要。

### `GET /api/v1/users/me` (需确认)
- **描述**: 使用有效的 `access_token` 获取当前登录用户的个人信息。
- **注意**: 此端点属于 `[[用户中心]]` 模块，需要确认其存在且受 `helios-api` 的 OAuth 2.0 保护。

## 5. 关联模块

### 5.1. 依赖于 (Depends On)
- `[[helios-api]]` (外部 OAuth 服务)

### 5.2. 被依赖于 (Depended On By)
- `[[订阅支付]]` (需要用户认证信息来处理支付)
- `[[用户输入处理]]` (可能需要根据用户登录状态调整功能)

## 6. 变更历史 (Change History)
- **v1.0 (2025-07-22, Initial):** 初始版本创建，定义了基于 `helios-api` 的 OAuth 认证流程。