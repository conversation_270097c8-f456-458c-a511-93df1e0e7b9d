# 模块设计文档: [[系统设置]]

**版本**: 1.0
**最后更新**: 2025-07-28
**最近变更类型**: Initial

---

## 1. 核心职责与边界
> 这个模块的核心职责是管理输入法的各种设置，包括用户偏好、快捷键配置、语言切换等。它负责存储和应用这些设置，但不直接处理输入逻辑或认证。
> 
> **不做**: 不处理用户认证（依赖[[用户认证]]）、不直接渲染UI（仅提供数据模型）。

## 2. 功能点与用户旅程
- **F1: 输入法菜单配置**
  - **描述**: 用户通过输入法菜单访问设置。模仿微信输入法风格：移除Deploy项，新增输入法设置、登录、场景切换（待开发）、目标语言切换（待开发）、帮助与反馈、关于输入法。
  - **依赖**: `[[用户认证]]` (登录功能), `[[核心交互逻辑]]` (菜单渲染)
- **F2: 设置存储与应用**
  - **描述**: 用户修改设置后，实时存储到UserDefaults或Keychain，并应用到输入引擎。
  - **依赖**: 无

## 3. 数据模型

### `Settings`
| 字段名 | 类型 | 约束/注释 |
| --- | --- | --- |
| id | String | PK |
| keyBindings | Dictionary<String, String> | 快捷键配置 |
| targetLanguage | String | 默认语言 |
| sceneMode | String | 场景模式（待开发） |

## 4. API 接口

### `GET /api/settings`
- **描述**: 获取当前设置。
- **响应**: JSON of Settings。
- **内部交互**: 无。

### `POST /api/settings/update`
- **描述**: 更新设置。
- **请求体**: Partial Settings JSON。
- **响应**: Success message。
- **内部交互**: 通知[[输入处理]]模块应用新设置。

## 5. 关联模块

### 5.1. 依赖于 (Depends On)
- `[[用户认证]]`
- `[[输入处理]]`

### 5.2. 被依赖于 (Depended On By)
- 使用 `archie show overview` 动态生成。

## 6. 变更历史 (Change History)
- **v1.0 (2025-07-28, Initial):** 初始版本创建。