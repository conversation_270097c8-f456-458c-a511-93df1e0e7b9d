# Siflowtype 技术开发规范

> **[!] 重要提示**: 
> 1. **开发环境须知**: 本项目采用 `xcodeproj` + VSCode 的混合开发模式。由于 `xcodeproj` 无法脱离 Xcode 环境独立编译运行，在 VSCode 中进行编码时，请遵循 Swift 和项目规范的最佳实践。所有代码变更将在 Xcode 中进行最终的编译和验证。如遇编译错误，相关反馈将被提供以进行修正。
> 2. 当规范内容指向一个独立的 `.md` 文件时（例如，`详细规范请参见：[Git提交规范.md](./Git提交规范.md)`），**必须**在开始相关任务前，完整阅读并理解所引用文件的全部内容。忽略此要求可能导致不符合项目标准的产出。

## 1. 概述

本文档定义了 Siflowtype 项目的技术选型、架构原则和开发规范，旨在确保代码质量、提升协作效率、降低维护成本。

## 2. 核心技术栈

项目基于以下核心技术栈构建，所有开发工作必须遵循这些选型：

- **语言**: Swift
- **UI 框架**: AppKit, SwiftUI
- **异步处理**: Combine, Swift Concurrency (async/await)
- **日志记录**: `OSLog`

## 3. 架构原则

项目遵循 **功能驱动（Feature-Driven）** 和 **MVVM (Model-View-ViewModel)** 的混合架构原则，旨在实现以下目标：

- **高内聚、低耦合**: 每个功能模块（Feature）内部高度聚合，模块间通过定义好的接口或服务进行通信。
- **可测试性**: `Service` 层和 `ViewModel` 层的逻辑应易于进行单元测试。
- **UI 独立性**: 业务逻辑和状态管理（ViewModel）与具体的 UI 实现（View）分离。

### 3.1. MVVM 详解

- **Model**: 负责数据的定义和存储。它不应包含任何业务逻辑，仅作为数据的载体。
- **View**: 负责 UI 的展示和用户交互的捕获。在 SwiftUI 中，View 是一个根据状态（State）进行渲染的函数。它应保持“哑”状态，将所有业务逻辑和用户操作委托给 ViewModel。
- **ViewModel**: 视图模型，是 View 和 Model 之间的桥梁。它负责：
  - 从 Model 获取数据，并将其转换为 View 可以直接绑定的格式。
  - 包含 View 的状态（如 `@Published` 属性）。
  - 实现业务逻辑和用户操作响应（如函数）。
  - **所有 ViewModel 都必须遵循 `ObservableObject` 协议。**

### 3.2. 分层结构

项目代码主要分为以下几个层次：

- **`Application`**: 程序入口，负责应用生命周期管理、AppDelegate 设置等。
- **`Features`**: 存放核心功能模块，每个子目录代表一个独立的功能单元（如 `Candidate`, `InputHandling`）。
  - **`View`**: SwiftUI 或 AppKit 视图，负责 UI 的渲染和用户交互的捕获。
  - **`ViewModel`**: 视图模型，负责处理视图的业务逻辑和状态管理，为 View 提供可绑定的数据。
  - **`Model`**: 与该功能相关的特定数据模型。
- **`Services`**: 提供跨功能共享的通用服务，如网络请求 (`OpenAIService`)、数据持久化 (`KeychainService`) 等。**服务通过 `AppContainer` 进行管理和访问。**
- **`Resources`**: 存放应用的资源文件，如 `Assets.xcassets`, `Info.plist` 等。
- **`Utilities`**: 存放全局辅助函数、扩展等。
- **`wiki`**: 存放项目的设计文档、需求文档和架构决策记录。

### 3.3. 项目结构树

## 6. 测试策略

为确保软件质量和稳定性，项目要求遵循以下测试策略：

- **端到端测试 (E2E Tests)**:
  - **范围**: 针对核心用户流程（如用户认证、核心输入功能）编写端到端测试。
  - **目的**: 验证整个应用作为一个整体是否能按预期工作。

```
.
├── Application/        # App 入口与生命周期管理
├── Features/           # 核心功能模块
│   ├── Candidate/      # 候选词面板功能
│   └── InputHandling/  # 输入处理功能
├── Resources/          # 资源文件
├── Services/           # 共享服务 (如 API Client)
├── Tests/              # 单元测试与 UI 测试
├── wiki/               # 系统设计文档
├── Siflowtype.xcodeproj
└── README.md
```

## 4. 架构核心：容器与环境

为了实现清晰的依赖管理、配置分离和高可测试性，项目采用**服务容器（Service Container）**和**环境配置（Environment Configuration）**相结合的模式。

### 4.1. 核心组件

- **`AppContainer` (服务容器)**: 一个遵循单例模式的 `final class`，作为所有共享服务的依赖容器。它负责在应用启动时实例化所有服务（如 `AuthService`, `OpenAIService`），并解决它们之间的依赖关系。通过 `AppContainer.shared`，应用的任何部分都可以安全地访问这些服务的单例实例。

- **`AppEnvironment` (环境配置)**: 一个 `struct`，负责管理应用在不同环境（如开发、生产）下的特定配置，例如 API 的 URL、OAuth 的客户端 ID 等。它通过编译标志（`#if DEBUG`）来自动选择合适的配置，并支持通过 `UserDefaults` 在开发时进行重载，为调试和测试提供了极大的灵活性。

### 4.2. 实现方案

**1. 定义 `AppEnvironment` (配置中心)**

```swift
// File: Application/AppEnvironment.swift

import Foundation

/// 定义了应用在不同环境下的特定配置协议
protocol AppEnvironmentConfiguration {
    var apiBaseURL: String { get }
    // ... 其他配置项
}

/// 生产环境配置
struct ProductionEnvironment: AppEnvironmentConfiguration { ... }

/// 开发环境配置 (支持 UserDefaults 重载)
struct DevelopmentEnvironment: AppEnvironmentConfiguration { ... }

/// AppEnvironment 根据编译标志选择合适的配置
struct AppEnvironment {
    static var current: AppEnvironmentConfiguration = {
        #if DEBUG
        return DevelopmentEnvironment()
        #else
        return ProductionEnvironment()
        #endif
    }()
}
```

**2. 定义 `AppContainer` (服务容器)**

```swift
// File: Application/AppContainer.swift

final class AppContainer {
    /// 全局共享的容器实例
    static let shared = AppContainer()

    // 持有所有服务的实例
    let authService: AuthService
    let openAIService: OpenAIService
    // ... 其他服务

    private init() {
        // 在此实例化所有服务，并注入依赖
        self.authService = AuthService()
        self.openAIService = OpenAIService(authService: self.authService)
    }
}
```

**3. 在代码中使用服务和配置**

在 `ViewModel` 或其他业务逻辑中，通过 `AppContainer.shared` 访问服务实例，通过 `AppEnvironment.current` 访问配置项。

```swift
// 在 ViewModel 中使用
@MainActor
class MyViewModel: ObservableObject {
    private let openAIService = AppContainer.shared.openAIService

    func fetchUserProfile() {
        let endpoint = AppEnvironment.current.userInfoEndpoint
        // ... 使用服务和配置进行操作
    }
}
```

**4. 在测试中注入模拟对象 (Mocks)**

由于 `AppContainer` 是一个单例，直接替换其服务实例较为困难。为了实现可测试性，服务本身的设计应遵循**依赖倒置原则**，即依赖于协议而非具体实现。在 `AppContainer` 中，可以根据测试目标注入模拟服务。

*注意：此模式简化了日常开发中的服务访问，但在严格的单元测试隔离方面做出了一定的权衡。对于需要深度测试的复杂服务，可以考虑为 `AppContainer` 提供一个可替换的测试版本。*

### 4.6. 类型定义与管理

- **文件与分组**: 将关联性强的类型定义（如错误、数据模型、常量）集中在同一个文件中，并根据其所属的功能模块或服务放置在相应的目录下，以实现高内聚。

- **使用 `enum` 定义状态与错误**: 优先使用 `enum` 来定义离散的状态和错误类型。对于错误，建议遵循 `LocalizedError` 协议，提供面向用户的错误描述和恢复建议。

- **优先使用 `struct` 定义数据模型**: 除非明确需要引用语义（`class`）的行为，否则应优先使用 `struct` 来创建数据模型。这可以利用值语义带来的安全性，防止意外的共享状态修改。

- **利用 `extension` 进行逻辑分组**: 使用 `extension` 来扩展现有类型的功能或进行逻辑上的代码组织。例如，为 `Notification.Name` 创建扩展来定义类型安全的通知名称，或将协议遵循（如 `Codable`, `Equatable`）的代码块分离出来。

- **协议驱动开发**: 通过定义协议来抽象具体实现，是实现依赖倒置和提高可测试性的关键。服务和核心组件应依赖于协议而非具体的类。

## 5. 开发规范

### 4.1. 编码命名规范

- **通用原则**: 遵循 Apple 官方的 [API Design Guidelines](https://www.swift.org/documentation/api-design-guidelines/)。
- **强制代码风格**: **项目必须集成 SwiftLint**，并遵循仓库根目录下的 `.swiftlint.yml` 配置文件中定义的规则，以确保代码风格的绝对一致性。
- **类型名 (Struct, Class, Enum, Protocol)**: 使用 `PascalCase`，如 `CandidateViewModel`。
- **变量与函数名**: 使用 `camelCase`，如 `fetchCandidates()`。
- **注释 (Comments)**:
  - **必要性**: 所有 `public` 或 `open` 的 API 都应有文档注释。
  - **核心逻辑**: 复杂算法或关键业务逻辑必须有注释，解释其实现思路和目的。
  - **简洁明了**: 注释应简洁、清晰，避免冗余。禁止提交被注释掉的代码。

### 4.2. 配置管理

- **静态配置**: 应用的基本配置（如 Bundle Identifier, 版本号）在 `Info.plist` 中管理。
- **用户设置**: 用户可配置的选项（如快捷键、API Key）使用 `UserDefaults` 进行存储。
- **敏感信息**: 严禁在代码或 `Info.plist` 中硬编码 API Key 等敏感信息。应引导用户在设置界面中输入，并安全地存储在 Keychain 中。

### 4.3. 常量与枚举

- **常量**: 使用 `let` 定义。对于全局或类型范围的常量，使用 `static let`。
- **枚举**: 充分利用 Swift 的 `enum` 类型，特别是有关联值的枚举，来清晰地表示状态或离散的数据集。

```swift
enum ViewState<T> {
    case loading
    case loaded(T)
    case error(Error)
}
```

### 4.4. 日志规范

项目采用双轨日志系统，结合使用 Apple 的 `OSLog` 和文件日志，以满足不同场景的需求。

- **控制台日志 (OSLog)**:
  - **统一使用 OSLog**: 所有实时调试和开发阶段的日志必须通过 `OSLog` 框架进行记录。
  - **结构化日志**: 利用 `OSLog` 的特性，提供 `subsystem` 和 `category` 来组织日志，便于在 Console.app 中实时过滤和分析。
  - **日志级别**:
    - `debug`: 用于开发阶段的详细调试信息。
    - `info`: 记录关键业务流程节点，如用户操作、状态变更等。
    - `error`: 记录可恢复的错误，不影响程序核心功能。
    - `fault`: 记录严重问题，可能导致功能异常或程序终止。
  - **日志内容**: 避免在日志中记录用户的个人数据或敏感信息。

- **文件日志 (File Logging)**:
  - **目的**: 为了便于用户反馈问题和开发者离线排查，关键日志需要被持久化到本地文件。
  - **存储位置**: 日志文件必须存储在 `~/Library/Logs/siflowtype.log`。
  - **记录内容**: 主要记录 `info`, `error`, `fault` 级别的日志。`debug` 级别的信息不应写入文件，以避免日志文件过大。
  - **格式**: 每条日志应包含时间戳、日志级别、`subsystem`、`category` 和消息文本，格式应清晰易读。
  - **轮转策略 (Rotation)**: 当日志文件达到一定大小（如 5MB）时，应自动进行归档和清理，以防止占用过多磁盘空间。系统应保留最近的3个归档文件。

### 4.5. 异步与线程管理

- **优先使用现代并发模型**: 必须优先使用 Swift 5.5+ 引入的 `async/await` 和 `Task` 来处理所有异步操作，以替代传统的闭包回调和 `DispatchQueue`。

- **确保 UI 更新在主线程**: 
  - 所有直接更新 UI 的属性或方法都必须在主线程上执行。**必须使用 `@MainActor` 来标记负责 UI 更新的类或方法**，以确保编译器级别的线程安全检查。
  - **严禁使用 `DispatchQueue.main.async`** 来手动调度 UI 更新，除非在无法使用 `async/await` 的旧代码或第三方库回调中。`@MainActor` 是首选方案。
  - **严禁直接使用 `DispatchQueue.global().async`** 进行后台任务调度。这种方式缺乏结构化并发的优势，容易导致任务失控和资源泄漏。应优先使用 `Task { ... }` 来创建非结构化任务，或在 `async` 函数中直接调用，以便利用 Swift Concurrency 的取消和错误传递机制。

- **ViewModel 中的线程管理**:
  - `ViewModel` 类应标记为 `@MainActor`，因为它的 `@Published` 属性会直接驱动 UI 更新。
  - 对于耗时的非 UI 操作（如网络请求、数据处理），应在 `ViewModel` 的方法中创建一个新的 `Task` 来执行，或调用另一个非 `@MainActor` 的 `Service` 层方法。这可以防止阻塞主线程。

```swift
@MainActor
class MyViewModel: ObservableObject {
    @Published var data: String = ""

    private let dataService: DataService // 非 MainActor 的服务

    func fetchData() {
        Task {
            do {
                // dataService.fetch() 在后台线程执行
                let result = try await dataService.fetch()
                // 回到 MainActor，安全地更新 UI 状态
                self.data = result
            } catch {
                // 处理错误
            }
        }
    }
}
```

### 5.7. UI 组件规范

- **可复用性**: 鼓励创建小而美的、可复用的 SwiftUI `View` 组件。
- **单一职责**: 每个 `View` 组件应只负责一块具体的 UI 展示，避免创建庞大而臃肿的“上帝视图”。
- **预览驱动**: 强烈建议为所有自定义 `View` 组件编写 `PreviewProvider`，以便在 Xcode Previews 中快速迭代和验证 UI。

### 5.8. 错误处理

- **明确的错误类型**: 使用自定义的 `enum` 并遵循 `Error` 协议来定义清晰的业务错误类型。
- **使用 `Result` 类型**: 在异步操作或可能失败的函数签名中，优先使用 `Result<Success, Failure>` 类型，使成功和失败路径的处理更加明确。
- **错误传递**: 在 `Service` 层和 `ViewModel` 层中，应将底层的错误（如网络错误）包装成自定义的业务错误再向上传递，避免上层逻辑依赖具体的实现细节。