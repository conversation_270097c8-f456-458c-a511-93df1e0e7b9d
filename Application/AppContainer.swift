//
//  AppContainer.swift
//  Siflowtype
//
//  Created by Ervin on 2024/7/31.
//

import Foundation
import OpenAI

/// `AppContainer` 遵循单例模式，作为所有共享服务的依赖容器。
///
/// 它负责在应用启动时实例化所有服务，并解决它们之间的依赖关系。
/// 通过 `AppContainer.shared`，应用的任何部分都可以安全地访问这些服务的单例实例。
final class AppContainer {
    /// 全局共享的容器实例。
    static let shared = AppContainer()

    // MARK: - Legacy Services (保持向后兼容)

    /// 负责处理用户认证，包括 OAuth 流程和 Token 管理。
    let authService: AuthService

    /// 封装了与 OpenAI API 的所有交互（旧版本，保持兼容性）。
    let openAIService: OpenAIService

    /// 提供安全的钥匙串（Keychain）读写功能，用于存储敏感信息。
    let keychainService: KeychainService

    // MARK: - New Architecture Services (新架构服务)

    /// 新的候选词服务，基于重构后的架构
    @MainActor let candidateService: CandidateService

    /// 提示词提供者，负责构建场景感知的提示词
    let promptProvider: PromptProvider

    /// 候选词生成器，负责执行 AI 调用
    let candidateGenerator: CandidateGenerator

    // MARK: - Initializers

    /// 私有化构造函数，确保全局只有一个实例。
    private init() {
        // 1. 初始化环境配置
        let environment = AppEnvironment.shared

        // 2. 实例化基础服务（保持向后兼容）
        self.keychainService = KeychainService()
        self.authService = AuthService(config: environment, keychainService: self.keychainService)
        self.openAIService = OpenAIService(config: environment, authService: self.authService)

        // 3. 实例化新架构的核心组件

        // 3.1 创建 OpenAI 客户端（用于新架构）
        let apiURL = URL(string: environment.apiBaseURL)
        let openAIClient = OpenAI(configuration: .init(
            token: environment.apiToken,
            host: apiURL?.host ?? "",
            scheme: apiURL?.scheme ?? "https"
        ))

        // 3.2 实例化服务组件
        self.promptProvider = GeneralPromptProvider()
        self.candidateGenerator = OpenAIGenerator(client: openAIClient)

        // 3.3 组装主要服务（在主线程上创建）
        // 先保存对属性的引用，避免在闭包中捕获未完全初始化的 self
        let promptProvider = self.promptProvider
        let candidateGenerator = self.candidateGenerator
        self.candidateService = MainActor.assumeIsolated {
            CandidateService(
                promptProvider: promptProvider,
                generator: candidateGenerator
            )
        }
    }
}
