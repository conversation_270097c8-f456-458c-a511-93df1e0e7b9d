//
//  ApplicationDelegate.swift
//  Siflowtype
//
//  Created by ervin on 2025/7/7.
//

import AppKit
import Foundation
import UserNotifications
import SwiftUI
import OSLog

@MainActor
final class SiflowtypeApplicationDelegate: NSObject, NSApplicationDelegate,
  UNUserNotificationCenterDelegate
{

  // MARK: - Properties
  
  /// AppCoordinator 负责协调应用的核心组件
  private(set) var appCoordinator: AppCoordinator?
  
  static let notificationIdentifier = "SiflowtypeNotification"
  
  private let logger = Logger(subsystem: "com.siflowtype.app", category: "ApplicationDelegate")
  
  // MARK: - Computed Properties for Backward Compatibility
  
  /// 为了保持向后兼容性，提供对 panel 的访问
  var panel: CandidatePanel? {
    return appCoordinator?.panel
  }
  


  //
  static func showMessage(msgText: String?) {
    let center = UNUserNotificationCenter.current()
    center.requestAuthorization(options: [.alert, .provisional]) { _, error in
      if let error = error {
        print("User notification authorization error: \(error.localizedDescription)")
      }
    }
    center.getNotificationSettings { settings in
      if (settings.authorizationStatus == .authorized
        || settings.authorizationStatus == .provisional) && settings.alertSetting == .enabled
      {
        let content = UNMutableNotificationContent()
        content.title = NSLocalizedString("Siflowtype", comment: "")
        if let msgText = msgText {
          content.subtitle = msgText
        }
        if #available(macOS 12.0, *) {
            content.interruptionLevel = .active
        }
        let request = UNNotificationRequest(
          identifier: Self.notificationIdentifier, content: content, trigger: nil)
        center.add(request) { error in
          if let error = error {
            print("User notification request error: \(error.localizedDescription)")
          }
        }
      }
    }
  }

  func applicationWillFinishLaunching(_ notification: Notification) {
    // 初始化 AppCoordinator 并设置应用
    appCoordinator = AppCoordinator()
    appCoordinator?.setupApplication()
    
    // 执行其他初始化
    setup()
  }

  func applicationWillTerminate(_ notification: Notification) {
    // swiftlint:disable:next notification_center_detachment
    NotificationCenter.default.removeObserver(self)
    DistributedNotificationCenter.default().removeObserver(self)
    
    // 清理 AppCoordinator
    appCoordinator?.tearDown()
    appCoordinator = nil
  }

  func applicationShouldTerminate(_ sender: NSApplication) -> NSApplication.TerminateReply {
    print("Siflowtype is quitting.")
    return .terminateNow
  }

  func setup() {
    // 设置通知中心
    UNUserNotificationCenter.current().delegate = self
    
    // 创建必要的目录
    createDirIfNotExist(path: SiflowtypeApp.userDir)
    createDirIfNotExist(path: SiflowtypeApp.appDir)
    createDirIfNotExist(path: SiflowtypeApp.logDir)
    
    logger.info("Application setup completed")
  }
  
  // MARK: - Legacy Methods (移除了具体实现，现在由 AppCoordinator 处理)
  
  // 这些方法保留是为了向后兼容，但实际逻辑已移至 AppCoordinator
  func problematicLaunchDetected() -> Bool {
    var detected = false
    let logFile = FileManager.default.temporaryDirectory.appendingPathComponent("siflowtype_launch.json", conformingTo: .json)
    // print("[DEBUG] archive: \(logFile)")
    do {
      let archive = try Data(contentsOf: logFile, options: [.uncached])
      let decoder = JSONDecoder()
      decoder.dateDecodingStrategy = .millisecondsSince1970
      let previousLaunch = try decoder.decode(Date.self, from: archive)
      if previousLaunch.timeIntervalSinceNow >= -2 {
        detected = true
      }
    } catch let error as NSError where error.domain == NSCocoaErrorDomain && error.code == NSFileReadNoSuchFileError {

    } catch {
      print("Error occurred during processing launch time archive: \(error.localizedDescription)")
      return detected
    }
    do {
      let encoder = JSONEncoder()
      encoder.dateEncodingStrategy = .millisecondsSince1970
      let record = try encoder.encode(Date())
      try record.write(to: logFile)
    } catch {
      print("Error occurred during saving launch time to archive: \(error.localizedDescription)")
    }
    return detected
  }

  private func createDirIfNotExist(path: URL) {
    let fileManager = FileManager.default
    if !fileManager.fileExists(atPath: path.path) {
      do {
        try fileManager.createDirectory(at: path, withIntermediateDirectories: true)
      } catch {
        print("Error creating user data directory: \(path.path)")
      }
    }
  }
}

extension NSApplication {
  var siflowtypeAppDelegate: SiflowtypeApplicationDelegate {
    self.delegate as! SiflowtypeApplicationDelegate
  }
}
