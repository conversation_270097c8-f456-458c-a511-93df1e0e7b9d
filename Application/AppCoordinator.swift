//
//  AppCoordinator.swift
//  Siflowtype
//
//  Created by SwiftCoder on 2025/1/23.
//

import AppKit
import Foundation
import OSLog
import SwiftUI

/// AppCoordinator 负责协调应用的核心组件，包括 UI 创建、菜单栏管理、URL Scheme 路由等。
/// 这将 SiflowtypeApplicationDelegate 从繁重的职责中解放出来，使其专注于应用生命周期管理。
@MainActor
final class AppCoordinator {
    // MARK: - Properties

    private let logger = Logger(subsystem: "com.siflowtype.app", category: "AppCoordinator")
    private let appContainer: AppContainer

    // UI Components
    var panel: CandidatePanel?
    var modeIndicatorPanel: ModeIndicatorPanel?

    // MARK: - Initializers

    init(appContainer: AppContainer = .shared) {
        self.appContainer = appContainer
    }

    // MARK: - Public Methods

    /// 初始化应用的核心组件
    func setupApplication() {
        setupPanel()
        setupModeIndicatorPanel()
        setupURLSchemeHandling()
        setupNotificationObservers()
        logger.info("AppCoordinator: Application setup completed")
    }

    /// 清理资源
    func tearDown() {
        NotificationCenter.default.removeObserver(self)
        panel?.hide()
        modeIndicatorPanel?.orderOut(nil)
        logger.info("AppCoordinator: Teardown completed")
    }

    /// 显示模式切换指示器
    func showModeIndicator(mode: InputMode, at inputPos: NSRect? = nil) {
        modeIndicatorPanel?.showModeIndicator(mode: mode, at: inputPos)
    }
    
    func showModeIndicator(mode: InputMode) {
        showModeIndicator(mode: mode, at: nil)
    }

    // MARK: - Private Setup Methods

    private func setupPanel() {
        panel = CandidatePanel(position: NSRect.zero)
        logger.debug("AppCoordinator: Panel created")
    }

    private func setupModeIndicatorPanel() {
        modeIndicatorPanel = ModeIndicatorPanel()
    }

    private func setupURLSchemeHandling() {
        let appleEventManager = NSAppleEventManager.shared()
        appleEventManager.setEventHandler(
            self,
            andSelector: #selector(handleGetURL(event:withReplyEvent:)),
            forEventClass: AEEventClass(kInternetEventClass),
            andEventID: AEEventID(kAEGetURL)
        )
        logger.debug("AppCoordinator: URL scheme handling setup completed")
    }

    private func setupNotificationObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(inputModeDidChange(_:)),
            name: .inputModeDidChange,
            object: nil
        )
        logger.debug("AppCoordinator: Notification observers setup completed")
    }

    // MARK: - Notification Handlers

    @objc private func inputModeDidChange(_ notification: Notification) {
        // 处理输入模式变更通知
        // 这里可以根据需要更新UI状态或执行其他操作
        if let mode = notification.userInfo?["mode"] as? String {
            // Input mode changed
        }
    }

    // MARK: - URL Scheme Handling

    @objc private func handleGetURL(event: NSAppleEventDescriptor, withReplyEvent _: NSAppleEventDescriptor) {
        guard let urlString = event.paramDescriptor(forKeyword: keyDirectObject)?.stringValue,
              let url = URL(string: urlString)
        else {
            logger.error("AppCoordinator: Invalid URL in handleGetURL")
            return
        }

        logger.info("AppCoordinator: Handling URL: \(urlString)")

        // 处理 OAuth 回调
        if url.scheme == "siflowtype", url.host == "auth" {
            handleOAuthCallback(url: url)
        }
    }

    private func handleOAuthCallback(url _: URL) {
        Task {
            do {
//                try await appContainer.authService.handleOAuthCallback(url: url)
//                logger.info("AppCoordinator: OAuth callback handled successfully")
            } catch {
                logger.error("AppCoordinator: OAuth callback error: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - NSApplication Extension

extension NSApplication {
    var appCoordinator: AppCoordinator? {
        return siflowtypeAppDelegate.appCoordinator
    }
}
