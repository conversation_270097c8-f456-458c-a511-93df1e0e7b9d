//
//  SiflowtypeApp.swift
//  Siflowtype
//
//  Created by ervin on 2025/7/7.

import Foundation
import InputMethodKit

@main
struct SiflowtypeApp {
    
    static let userDir = if let pwuid = getpwuid(getuid()) {
      URL(fileURLWithFileSystemRepresentation: pwuid.pointee.pw_dir, isDirectory: true, relativeTo: nil).appendingPathComponent("Library").appendingPathComponent("Siflowtype")
    } else {
      try! FileManager.default.url(for: .libraryDirectory, in: .userDomainMask, appropriateFor: nil, create: false).appendingPathComponent("Siflowtype", isDirectory: true)
    }
    
    static let appDir = "/Library/Input Library/Siflowtype.app".withCString { dir in
        URL(fileURLWithFileSystemRepresentation: dir, isDirectory: false, relativeTo: nil)
    }
    
    static let logDir = FileManager.default.temporaryDirectory.appendingPathComponent("siflowtype", isDirectory: true)
    
    static func main() {
      autoreleasepool {
        // find the bundle identifier and then initialize the input method server
        let main = Bundle.main
        let connectionName = main.object(forInfoDictionaryKey: "InputMethodConnectionName") as! String
        _ = IMKServer(name: connectionName, bundleIdentifier: main.bundleIdentifier!)
        // load the bundle explicitly because in this case the input method is a
        // background only application
        let app = NSApplication.shared
        let delegate = SiflowtypeApplicationDelegate()
        app.delegate = delegate
        app.setActivationPolicy(.accessory)

        // opencc will be configured with relative dictionary paths
        FileManager.default.changeCurrentDirectoryPath(main.sharedSupportPath!)

        if NSApp.siflowtypeAppDelegate.problematicLaunchDetected() {
          print("Problematic launch detected!")
          let args = ["Problematic launch detected! Siflowtype may be suffering a crash due to improper configuration. Revert previous modifications to see if the problem recurs."]
          let task = Process()
          task.executableURL = "/usr/bin/say".withCString { dir in
            URL(fileURLWithFileSystemRepresentation: dir, isDirectory: false, relativeTo: nil)
          }
          task.arguments = args
          try? task.run()
        } else {
          NSApp.siflowtypeAppDelegate.setup()
        }
        // finally run everything
        app.run()
        print(" Siflowtype is quitting...")
      }
      return
    }
}
