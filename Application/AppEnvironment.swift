//
//  AppEnvironment.swift
//  Siflowtype
//
//  Created by <PERSON><PERSON> on 2024/7/31.
//

import Foundation

/// 定义了应用在不同环境（开发、生产）下的特定配置。
protocol AppEnvironmentConfiguration {
    var apiBaseURL: String { get }
    var userInfoEndpoint: String { get }
    var oauthClientId: String { get }
    var oauthRedirectURI: String { get }
    var oauthScope: String { get }
    var oauthAuthorizeEndpoint: String { get }
    var oauthTokenEndpoint: String { get }
    var oauthRevokeEndpoint: String { get }
    var apiToken: String { get }
}

/// 生产环境配置
struct ProductionEnvironment: AppEnvironmentConfiguration {
    let apiBaseURL: String = "https://openrouter.ai/api/v1"
    let apiToken: String = "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"
    let userInfoEndpoint: String = "/api/v1/users/me"
    let oauthClientId: String = "siflowtype-macos-client"
    let oauthRedirectURI: String = "siflowtype://auth/callback"
    let oauthScope: String = "user:profile payment:subscribe"
    let oauthAuthorizeEndpoint: String = "/api/v1/oauth/authorize"
    let oauthTokenEndpoint: String = "/api/v1/oauth/token"
    let oauthRevokeEndpoint: String = "/api/v1/oauth/revoke"
}

/// 开发环境配置
struct DevelopmentEnvironment: AppEnvironmentConfiguration {
    let apiBaseURL: String = "https://openrouter.ai/api/v1"
    let apiToken: String = "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"
    let userInfoEndpoint: String = "/api/v1/users/me"
    let oauthClientId: String = "siflowtype-macos-dev"
    let oauthRedirectURI: String = "siflowtype://auth/callback"
    let oauthScope: String = "openid profile email"
    let oauthAuthorizeEndpoint: String = "/api/v1/oauth/authorize"
    let oauthTokenEndpoint: String = "/api/v1/oauth/token"
    let oauthRevokeEndpoint: String = "/api/v1/oauth/revoke"
}

/// AppEnvironment 负责管理应用的全局配置和状态。
/// 它根据当前的编译标志（DEBUG）选择合适的配置。
enum AppEnvironment {
    /// 当前激活的配置
    static var shared: AppEnvironmentConfiguration = {
        #if DEBUG
            return DevelopmentEnvironment()
        #else
            return ProductionEnvironment()
        #endif
    }()

    /// PKCE（Proof Key for Code Exchange）相关常量
    enum PKCE {
        static let codeVerifierLength = 32
        static let codeChallengeMethod = "S256"
    }

    /// 生成用于 OAuth 流程的 state 参数，以防止 CSRF 攻击。
    static func generateState() -> String {
        return UUID().uuidString
    }

    /// 根据端点构建完整的 URL
    static func url(for endpoint: String) -> URL? {
        return URL(string: shared.apiBaseURL + endpoint)
    }
}
