<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Siflowtype</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIconName</key>
	<string>AppIcon</string>
	<key>CFBundleIdentifier</key>
	<string>com.siflowtype.inputmethod.Siflowtype</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>zh-Hans</string>
	</array>
	<key>CFBundleName</key>
	<string>Siflowtype</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>ComponentInputModeDict</key>
	<dict>
		<key>tsInputModeListKey</key>
		<dict>
			<key>com.siflowtype.inputmethod.Siflowtype.English</key>
			<dict>
				<key>TISInputSourceID</key>
				<string>com.siflowtype.inputmethod.Siflowtype.English</string>
				<key>TISIntendedLanguage</key>
				<string>en-US</string>
				<key>tsInputModeAlternateMenuIconFileKey</key>
				<string>siflowtype.pdf</string>
				<key>tsInputModeCharacterRepertoireKey</key>
				<array>
					<string>English</string>
				</array>
				<key>tsInputModeDefaultStateKey</key>
				<true/>
				<key>tsInputModeIsVisibleKey</key>
				<true/>
				<key>tsInputModeKeyEquivalentModifiersKey</key>
				<integer>4608</integer>
				<key>tsInputModeMenuIconFileKey</key>
				<string>siflowtype.pdf</string>
				<key>tsInputModePaletteIconFileKey</key>
				<string>siflowtype.pdf</string>
				<key>tsInputModePrimaryInScriptKey</key>
				<true/>
				<key>tsInputModeScriptKey</key>
				<string>smUnicodeScript</string>
			</dict>
		</dict>
		<key>tsVisibleInputModeOrderedArrayKey</key>
		<array>
			<string>com.siflowtype.inputmethod.Siflowtype.English</string>
		</array>
	</dict>
	<key>InputMethodConnectionName</key>
	<string>Siflowtype_Connection</string>
	<key>InputMethodServerControllerClass</key>
	<string>Siflowtype.SiflowtypeInputController</string>
	<key>InputMethodServerDelegateClass</key>
	<string>Siflowtype.SiflowtypeInputController</string>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.utilities</string>
	<key>LSBackgroundOnly</key>
	<false/>
	<key>LSUIElement</key>
	<true/>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>TICapsLockLanguageSwitchCapable</key>
	<true/>
</dict>
</plist>
