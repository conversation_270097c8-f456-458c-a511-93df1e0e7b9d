//
//  ScenarioContext.swift
//  Siflowtype
//
//  Created by Ervin on 2024/8/1.
//

import Foundation

/// 用于封装场景的描述信息。
///
/// `ScenarioContext` 提供了一个标准化的方式来描述当前的操作场景，
/// 使得 AI 服务能够根据不同的上下文生成更精准的候选词。
struct ScenarioContext: Hashable, Identifiable {
    /// A unique identifier for the scenario, generated from its description.
    let id: String

    /// A natural language description of the current context or scenario.
    let description: String

    /// 初始化场景上下文
    /// - Parameter description: 场景的自然语言描述
    init(description: String) {
        self.description = description
        self.id = "\(description.hashValue)"
    }

    /// 通用场景上下文，适用于所有翻译和文本改进任务
    static let general = ScenarioContext(description: "Universal text improvement context. Intelligently analyze input language (sourceLanguage) and provide accurate, fluent, contextually appropriate (targetLanguage) translation and improvement suggestions. Automatically adjust expression style based on content characteristics.")
}
