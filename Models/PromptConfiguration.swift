//
//  PromptConfiguration.swift
//  Siflowtype
//
//  Created by Ervin on 2024/8/1.
//

import Foundation
import OpenAI

// MARK: - 提示词配置结构

/// 提示词配置结构
///
/// 包含了生成候选词所需的所有配置信息。
struct PromptConfiguration {
    /// 系统级提示词，定义 AI 的角色和行为
    let systemPrompt: String

    /// 用户提示词模板，包含占位符用于动态替换
    let userPromptTemplate: String

    /// 要使用的 AI 模型标识符
    let model: String

    /// 最大输入长度限制
    let maxInputLength: Int

    /// 源语言（输入语言）
    /// 支持的语言代码：
    /// - "en": English (英语)
    /// - "zh": Chinese (中文)
    /// - "ja": Japanese (日语)
    /// - "ko": Korean (韩语)
    /// - "fr": French (法语)
    /// - "de": German (德语)
    /// - "es": Spanish (西班牙语)
    /// - "auto": 自动检测
    let sourceLanguage: String

    /// 目标语言（输出语言）
    /// 支持的语言代码：
    /// - "en": English (英语)
    /// - "zh": Chinese (中文)
    /// - "ja": Japanese (日语)
    /// - "ko": Korean (韩语)
    /// - "fr": French (法语)
    /// - "de": German (德语)
    /// - "es": Spanish (西班牙语)
    /// - "auto": 根据源语言智能推断
    let targetLanguage: String

    /// 返回提示的行数（建议数量）
    let suggestionCount: Int

    /// 初始化提示词配置
    /// - Parameters:
    ///   - systemPrompt: 系统级提示词
    ///   - userPromptTemplate: 用户提示词模板
    ///   - model: AI 模型标识符
    ///   - maxInputLength: 最大输入长度
    ///   - sourceLanguage: 源语言（输入语言）
    ///   - targetLanguage: 目标语言（输出语言）
    ///   - suggestionCount: 返回提示的行数（建议数量）
    init(
        systemPrompt: String,
        userPromptTemplate: String,
        model: String = "google/gemini-flash-1.5-8b",
        maxInputLength: Int = 1000,
        sourceLanguage: String = "en-US",
        targetLanguage: String = "zh-CN",
        suggestionCount: Int = 3
    ) {
        self.systemPrompt = systemPrompt
        self.userPromptTemplate = userPromptTemplate
        self.model = model
        self.maxInputLength = maxInputLength
        self.sourceLanguage = sourceLanguage
        self.targetLanguage = targetLanguage
        self.suggestionCount = suggestionCount
    }
}

// MARK: - 扩展方法

extension PromptConfiguration {
    /// 构建用户提示词，将模板中的占位符替换为实际值
    /// - Parameter userInput: 用户输入的文本
    /// - Returns: 构建完成的用户提示词
    func buildUserPrompt(with userInput: String) -> String {
        return userPromptTemplate
            .replacingOccurrences(of: "{user_input}", with: userInput)
            .replacingOccurrences(of: "{sourceLanguage}", with: sourceLanguage)
            .replacingOccurrences(of: "{targetLanguage}", with: targetLanguage)
            .replacingOccurrences(of: "{suggestionCount}", with: "\(suggestionCount)")
    }

    /// 验证输入长度是否符合要求
    /// - Parameter input: 用户输入
    /// - Returns: 是否有效
    func isValidInput(_ input: String) -> Bool {
        return input.count <= maxInputLength && !input.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
}
