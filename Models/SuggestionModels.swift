//
//  SuggestionModels.swift
//  Siflowtype
//
//  Created by <PERSON><PERSON> on 2024/8/1.
//

import Foundation
import OpenAI // For JSONSchemaConvertible

/// Represents a single suggestion pair returned to the user interface.
///
/// 这是返回给用户界面的最终数据结构，包含了 AI 生成的完成文本和相应的解释。
struct SuggestionPair: Codable {
    /// The final, processed text generated by the AI.
    let completed: String

    /// An explanation, note, or the original text corresponding to the completed text.
    let explanation: String

    /// 便利初始化方法
    /// - Parameters:
    ///   - completed: AI 生成的完成文本
    ///   - explanation: 对应的解释或说明
    init(completed: String, explanation: String) {
        self.completed = completed
        self.explanation = explanation
    }
}

/// The data structure expected from the AI's JSON response.
///
/// 这是 AI 返回的原始 JSON 响应的数据结构，需要遵循 OpenAI 的 JSON Schema 规范。
struct CandidateResponse: Decodable, JSONSchemaConvertible {
    let suggestions: [SuggestionPair]

    /// 提供一个示例响应，用于 JSON Schema 生成和测试
    static let example: Self = .init(
        suggestions: [
            .init(completed: "Remove duplicates from the list", explanation: "从列表中移除重复项"),
            .init(completed: "Deduplicate the list", explanation: "对列表进行去重"),
            .init(completed: "Filter out duplicate items from the list", explanation: "过滤列表中的重复项"),
        ]
    )
}

// MARK: - 扩展方法

extension CandidateResponse {
    /// 将 AI 响应转换为用户界面所需的 SuggestionPair 数组
    /// - Returns: SuggestionPair 数组
    func toSuggestionPairs() -> [SuggestionPair] {
        return suggestions
    }
}

extension SuggestionPair: Equatable {
    static func == (lhs: SuggestionPair, rhs: SuggestionPair) -> Bool {
        return lhs.completed == rhs.completed && lhs.explanation == rhs.explanation
    }
}
