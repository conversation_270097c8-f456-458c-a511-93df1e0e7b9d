//
//  GeneratorError.swift
//  Siflowtype
//
//  Created by Ervin on 2024/8/1.
//

import Foundation

/// 定义具有丰富上下文的、精细化的错误类型。
/// 
/// `GeneratorError` 提供了详细的错误信息，包括用户友好的描述和调试信息，
/// 帮助开发者和用户更好地理解和处理各种错误情况。
enum GeneratorError: Error, LocalizedError {
    /// 请求超时错误
    case requestTimedOut(after: TimeInterval)
    
    /// AI 模型没有返回内容
    case noResponseContent(model: String)
    
    /// 响应解析失败
    case responseParsingFailed(model: String, rawResponse: String, underlyingError: Error)
    
    /// API 错误
    case apiError(statusCode: Int?, message: String)
    
    /// 输入验证失败
    case inputValidationFailed(reason: String)
    
    /// 配置错误
    case configurationError(reason: String)
    
    /// 网络连接错误
    case networkError(underlyingError: Error)
    
    // MARK: - LocalizedError 协议实现
    
    /// 用户友好的错误描述
    var errorDescription: String? {
        switch self {
        case .requestTimedOut(let timeout):
            return "请求超时（\(timeout)秒）。请检查网络连接并重试。"
        case .noResponseContent(let model):
            return "AI 模型（\(model)）没有返回有效内容。请重试。"
        case .responseParsingFailed(let model, _, _):
            return "无法解析 AI 模型（\(model)）的响应。请重试。"
        case .apiError(let statusCode, let message):
            if let code = statusCode {
                return "API 错误（状态码：\(code)）：\(message)"
            } else {
                return "API 错误：\(message)"
            }
        case .inputValidationFailed(let reason):
            return "输入验证失败：\(reason)"
        case .configurationError(let reason):
            return "配置错误：\(reason)"
        case .networkError(_):
            return "网络连接错误。请检查网络连接并重试。"
        }
    }
    
    /// 详细的调试描述，用于日志记录
    var debugDescription: String {
        switch self {
        case .requestTimedOut(let timeout):
            return "GeneratorError.requestTimedOut: Request timed out after \(timeout) seconds"
        case .noResponseContent(let model):
            return "GeneratorError.noResponseContent: Model '\(model)' returned no content"
        case .responseParsingFailed(let model, let rawResponse, let underlyingError):
            return "GeneratorError.responseParsingFailed: Failed to parse response from model '\(model)'. Raw response: '\(rawResponse)'. Underlying error: \(underlyingError)"
        case .apiError(let statusCode, let message):
            return "GeneratorError.apiError: Status code: \(statusCode?.description ?? "unknown"), Message: \(message)"
        case .inputValidationFailed(let reason):
            return "GeneratorError.inputValidationFailed: \(reason)"
        case .configurationError(let reason):
            return "GeneratorError.configurationError: \(reason)"
        case .networkError(let underlyingError):
            return "GeneratorError.networkError: \(underlyingError)"
        }
    }
    
    /// 失败原因描述
    var failureReason: String? {
        switch self {
        case .requestTimedOut(_):
            return "网络请求超时"
        case .noResponseContent(_):
            return "AI 模型未返回内容"
        case .responseParsingFailed(_, _, _):
            return "响应格式解析失败"
        case .apiError(_, _):
            return "API 服务错误"
        case .inputValidationFailed(_):
            return "输入数据验证失败"
        case .configurationError(_):
            return "服务配置错误"
        case .networkError(_):
            return "网络连接失败"
        }
    }
    
    /// 恢复建议
    var recoverySuggestion: String? {
        switch self {
        case .requestTimedOut(_):
            return "请检查网络连接状态，稍后重试。"
        case .noResponseContent(_):
            return "请重新发送请求，或尝试简化输入内容。"
        case .responseParsingFailed(_, _, _):
            return "这可能是临时问题，请重试。如果问题持续，请联系技术支持。"
        case .apiError(_, _):
            return "请稍后重试。如果问题持续，请联系技术支持。"
        case .inputValidationFailed(_):
            return "请检查并修正输入内容。"
        case .configurationError(_):
            return "请检查应用配置或重启应用。"
        case .networkError(_):
            return "请检查网络连接并重试。"
        }
    }
}

// MARK: - 便利方法

extension GeneratorError {
    /// 判断错误是否可以重试
    var isRetryable: Bool {
        switch self {
        case .requestTimedOut(_), .noResponseContent(_), .apiError(_, _), .networkError(_):
            return true
        case .responseParsingFailed(_, _, _), .inputValidationFailed(_), .configurationError(_):
            return false
        }
    }
    
    /// 获取建议的重试延迟时间（秒）
    var suggestedRetryDelay: TimeInterval {
        switch self {
        case .requestTimedOut(_):
            return 2.0
        case .noResponseContent(_):
            return 1.0
        case .apiError(let statusCode, _):
            // 根据状态码调整重试延迟
            if let code = statusCode, code >= 500 {
                return 5.0 // 服务器错误，延迟更长
            }
            return 2.0
        case .networkError(_):
            return 3.0
        default:
            return 1.0
        }
    }
}
