import Foundation
import InputMethodKit

@MainActor
final class SiflowtypeInputController: IMKInputController {
  private static let keyRollOver = 50
  private static var unknownAppCnt: UInt = 0

  private weak var client: IMKTextInput?
  private let inputEngine = InputEngine()
  private let inputViewModel = InputViewModel()
  private var lastModifiers: NSEvent.ModifierFlags = .init()
  private var currentApp: String = ""

  override func handle(_ event: NSEvent!, client _: Any!) -> Bool {
    guard let event = event else { return false }
    // 处理模式切换
    if inputViewModel.handleModeToggleKeyEvent(event) {
      return true
    }

    // 英文模式下的处理：只处理修饰键状态更新，其他按键交给系统
    if inputViewModel.currentMode == .english {
      return handleEnglishModeEvent(event)
    }

    // AI模式下的按键处理
    return handleAIModeEvent(event)
  }

  // MARK: - Private Event Handling

  private func handleEnglishModeEvent(_ event: NSEvent) -> Bool {
    switch event.type {
    case .flagsChanged:
      // 更新修饰键状态以保持内部状态同步
      lastModifiers = event.modifierFlags
      return true
    case .keyDown:
      // 所有按键事件交给系统处理
      return false
    default:
      return false
    }
  }

  private func handleAIModeEvent(_ event: NSEvent) -> Bool {
    let modifiers = event.modifierFlags
    let result: InputEngineResult

    switch event.type {
    case .flagsChanged:
      result = handleModifierChanges(event: event, modifiers: modifiers)
    case .keyDown:
      result = handleKeyDown(event: event, modifiers: modifiers)
    default:
      return false
    }

    handleEngineResult(result)
    return result.handled
  }

  private func handleModifierChanges(event: NSEvent, modifiers: NSEvent.ModifierFlags) -> InputEngineResult {
    // 如果修饰键状态没有变化，直接返回
    guard lastModifiers != modifiers else {
      return InputEngineResult()
    }

    let changes = lastModifiers.symmetricDifference(modifiers)
    var result = InputEngineResult()

    // 处理 CapsLock
    if changes.contains(.capsLock) {
      result = inputEngine.handleKey(
        keyCode: event.keyCode,
        character: nil,
        modifiers: modifiers,
        isRelease: false
      )
    }

    // 处理其他修饰键的按下和释放
    let modifierFlags: [NSEvent.ModifierFlags] = [.shift, .control, .option, .command]
    for flag in modifierFlags where changes.contains(flag) {
      let isPress = modifiers.contains(flag)
      let keyResult = inputEngine.handleKey(
        keyCode: event.keyCode,
        character: nil,
        modifiers: modifiers,
        isRelease: !isPress
      )
      if keyResult.handled {
        result = keyResult
      }
    }

    lastModifiers = modifiers
    return result
  }

  private func handleKeyDown(event: NSEvent, modifiers: NSEvent.ModifierFlags) -> InputEngineResult {
    // 忽略 Command 组合键
    guard !modifiers.contains(.command) else {
      return InputEngineResult()
    }

    return inputEngine.handleKey(
      keyCode: event.keyCode,
      character: event.characters?.first,
      modifiers: modifiers,
      isRelease: false
    )
  }

  func selectCandidate(_ index: Int) -> Bool {
    let result = inputEngine.selectCandidate(at: index)
    handleEngineResult(result)
    return result.handled
  }

  func moveCaret(forward: Bool) -> Bool {
    let result = inputEngine.moveCaret(forward: forward)
    handleEngineResult(result)
    return result.handled
  }

  override func recognizedEvents(_: Any!) -> Int {
    return Int(NSEvent.EventTypeMask.Element(arrayLiteral: .keyDown, .flagsChanged).rawValue)
  }

  override func activateServer(_ sender: Any!) {
    client = sender as? IMKTextInput
    // 设置键盘布局（如果需要）
    var keyboardLayout = ""
    if keyboardLayout == "last" || keyboardLayout == "" {
      keyboardLayout = ""
    } else if keyboardLayout == "default" {
      keyboardLayout = "com.apple.keylayout.ABC"
    } else if !keyboardLayout.hasPrefix("com.apple.keylayout.") {
      keyboardLayout = "com.apple.keylayout.\(keyboardLayout)"
    }
    if keyboardLayout != "" {
      client?.overrideKeyboard(withKeyboardNamed: keyboardLayout)
    }
    inputEngine.activateServer()
  }

  override init!(server: IMKServer!, delegate: Any!, client: Any!) {
    self.client = client as? IMKTextInput
    super.init(server: server, delegate: delegate, client: client)
    inputEngine.delegate = self
    inputViewModel.delegate = self

    if let app = (client as? IMKTextInput)?.bundleIdentifier() {
      createSessionIfNeeded(for: app)
    }
  }

  override func deactivateServer(_ sender: Any!) {
    hidePalettes()
    commitComposition(sender)
    client = nil
  }

  override func hidePalettes() {
    (NSApp.delegate as? SiflowtypeApplicationDelegate)?.panel?.hide()
    super.hidePalettes()
  }

  override func commitComposition(_ sender: Any!) {
    client = sender as? IMKTextInput
    let result = inputEngine.commitComposition()
    handleEngineResult(result)
  }

  override func menu() -> NSMenu! {
    let menu = NSMenu()

    let settingsItem = NSMenuItem(title: "输入法设置", action: #selector(openSettings), keyEquivalent: "")
    settingsItem.target = self
    menu.addItem(settingsItem)

    let loginItem = NSMenuItem(title: "登录", action: #selector(openLogin), keyEquivalent: "")
    loginItem.target = self
    menu.addItem(loginItem)

    let sceneSwitchItem = NSMenuItem(title: "场景切换（待开发）", action: #selector(switchScene), keyEquivalent: "")
    sceneSwitchItem.target = self
    menu.addItem(sceneSwitchItem)

    let languageSwitchItem = NSMenuItem(title: "目标语言切换（待开发）", action: #selector(switchLanguage), keyEquivalent: "")
    languageSwitchItem.target = self
    menu.addItem(languageSwitchItem)

    let helpItem = NSMenuItem(title: "帮助与反馈", action: #selector(openHelp), keyEquivalent: "")
    helpItem.target = self
    menu.addItem(helpItem)

    let aboutItem = NSMenuItem(title: "关于输入法", action: #selector(openAbout), keyEquivalent: "")
    aboutItem.target = self
    menu.addItem(aboutItem)

    return menu
  }

  @objc func openSettings() {
    // TODO: Implement settings opening logic
    print("Opening input method settings")
  }

  @objc func openLogin() {
    // Integrate with AuthenticationManager
//    AuthenticationManager.shared.startLogin()
  }

  @objc func switchScene() {
    // TODO: Implement scene switching
    print("Switching scene (to be developed)")
  }

  @objc func switchLanguage() {
    // TODO: Implement language switching
    print("Switching target language (to be developed)")
  }

  @objc func openHelp() {
    // TODO: Open help and feedback
    print("Opening help and feedback")
  }

  @objc func openAbout() {
    // TODO: Open about information
    print("Opening about input method")
  }
}

// MARK: - InputEngineDelegate

extension SiflowtypeInputController: InputEngineDelegate {
  func inputEngine(_: InputEngine, didRequestCommit text: String) {
    commit(string: text)
  }

  func inputEngine(
    _: InputEngine, didRequestUpdateUI preedit: String, selRange: NSRange, caretPos: Int
  ) {
    show(preedit: preedit, selRange: selRange, caretPos: caretPos)
  }

  func inputEngine(_: InputEngine, didRequestShowPanel data: PanelData) {
    showPanel(
      preedit: data.preedit, selRange: data.selRange, caretPos: data.caretPos,
      candidateItems: data.candidateItems, highlighted: data.highlighted
    )
  }

  func inputEngine(_: InputEngine, didRequestHidePalettes _: Bool) {
    hidePalettes()
  }
}

// MARK: - InputViewModelDelegate

extension SiflowtypeInputController: InputViewModelDelegate {
  func commitText(_ text: String) {
    commit(string: text)
  }

  func showModeIndicator(mode: InputMode) {
    if let appCoordinator = NSApplication.shared.appCoordinator {
      // 获取输入位置
      var inputPos = NSRect()
      if let client = client {
        client.attributes(forCharacterIndex: 0, lineHeightRectangle: &inputPos)
        appCoordinator.showModeIndicator(mode: mode, at: inputPos)
      } else {
        appCoordinator.showModeIndicator(mode: mode)
      }
    }
  }

  func inputModeDidChange(to _: InputMode) {
    // 处理输入模式变更，可以在这里添加必要的逻辑
    // 例如：更新UI状态、重置输入引擎状态等
  }
}

private extension SiflowtypeInputController {
  func createSessionIfNeeded(for appBundleId: String) {
    inputEngine.createSession(for: appBundleId)
  }

  // 处理 InputEngine 返回的结果
  func handleEngineResult(_ result: InputEngineResult) {
    print("[InputController] handleEngineResult called:")
    print("  - handled: \(result.handled)")
    print("  - shouldCommit: \(result.shouldCommit)")
    print("  - shouldUpdateUI: \(result.shouldUpdateUI)")
    print("  - shouldShowPanel: \(result.shouldShowPanel)")
    print("  - shouldHidePalettes: \(result.shouldHidePalettes)")
    
    if result.shouldCommit, let text = result.commitText {
      print("[InputController] Committing text: '\(text)'")
      commit(string: text)
    }

    if result.shouldUpdateUI {
      print("[InputController] Updating UI")
      updateUI()
    }

    if result.shouldShowPanel, let panelData = result.panelData {
      print("[InputController] Showing panel with preedit: '\(panelData.preedit)'")
      showPanel(
        preedit: panelData.preedit,
        selRange: panelData.selRange,
        caretPos: panelData.caretPos,
        candidateItems: panelData.candidateItems,
        highlighted: panelData.highlighted
      )
    }

    if result.shouldHidePalettes {
      print("[InputController] Hiding palettes")
      hidePalettes()
    }
  }

  // 更新UI显示
  func updateUI() {
    let (preedit, selRange, caretPos) = inputEngine.getCurrentState()
    
    print("[InputController] updateUI called:")
    print("  - preedit: '\(preedit)'")
    print("  - selRange: \(selRange)")
    print("  - caretPos: \(caretPos)")
    print("  - candidatePreview: \(inputEngine.getCandidatePreview() ?? "nil")")

    if let candidatePreview = inputEngine.getCandidatePreview() {
      print("[InputController] Using candidate preview: '\(candidatePreview)'")
      show(preedit: candidatePreview, selRange: selRange, caretPos: candidatePreview.count)
    } else {
      // 当没有候选词预览时，直接使用引擎的预编辑文本更新UI
      // 如果 preedit 为空，show() 方法会负责清除客户端的标记文本
      print("[InputController] Using engine preedit: '\(preedit)'")
      show(preedit: preedit, selRange: selRange, caretPos: caretPos)
    }

    // 显示候选词面板
    if inputEngine.shouldShowPanel(), let panelData = inputEngine.getPanelData() {
      print("[InputController] Showing panel with \(panelData.candidateItems.count) candidates")
      showPanel(
        preedit: panelData.preedit,
        selRange: panelData.selRange,
        caretPos: panelData.caretPos,
        candidateItems: panelData.candidateItems,
        highlighted: panelData.highlighted
      )
    } else {
      print("[InputController] Hiding palettes")
      hidePalettes()
    }
  }

  func commit(string: String) {
    guard let client = client else { return }
    client.insertText(string, replacementRange: NSRange())
    hidePalettes()

    // 重置面板状态
    if let panel = (NSApp.delegate as? SiflowtypeApplicationDelegate)?.panel {
      panel.viewModel.reset()
    }
  }

  func show(preedit: String, selRange: NSRange, caretPos: Int) {
    guard let client = client else { 
      print("[InputController] show() called but no client available")
      return 
    }
    
    print("[InputController] show() called:")
    print("  - preedit: '\(preedit)'")
    print("  - selRange: \(selRange)")
    print("  - caretPos: \(caretPos)")

    // 如果预编辑文本为空，清除标记文本
    if preedit.isEmpty {
      print("[InputController] Clearing marked text")
      client.setMarkedText("", selectionRange: NSRange(location: 0, length: 0),
                           replacementRange: NSRange(location: NSNotFound, length: 0))
      print("[InputController] client.setMarkedText completed (clear)")
      return
    }

    let start = selRange.location
    let attrString = NSMutableAttributedString(string: preedit)
    if start > 0 {
      let attrs =
        mark(forStyle: kTSMHiliteConvertedText, at: NSRange(location: 0, length: start))!
          as! [NSAttributedString.Key: Any]
      attrString.setAttributes(attrs, range: NSRange(location: 0, length: start))
    }
    let remainingRange = NSRange(location: start, length: preedit.utf16.count - start)
    let attrs =
      mark(forStyle: kTSMHiliteSelectedRawText, at: remainingRange)!
        as! [NSAttributedString.Key: Any]
    attrString.setAttributes(attrs, range: remainingRange)

    // 使用 NSNotFound 表示不替换任何内容，在当前光标位置插入
    print("[InputController] Calling client.setMarkedText with: '\(preedit)'")
    client.setMarkedText(
      attrString, selectionRange: NSRange(location: caretPos, length: 0),
      replacementRange: NSRange(location: NSNotFound, length: 0)
    )
    print("[InputController] client.setMarkedText completed")
  }

  // swiftlint:disable:next function_parameter_count
  func showPanel(
    preedit: String, selRange: NSRange, caretPos: Int, candidateItems: [CandidateItem],
    highlighted: Int
  ) {
    // print("[DEBUG] showPanelWithPreedit:...:")
    guard let client = client else { return }
    var inputPos = NSRect()
    client.attributes(forCharacterIndex: 0, lineHeightRectangle: &inputPos)
    if let panel = (NSApp.delegate as? SiflowtypeApplicationDelegate)?.panel {
      panel.inputController = self
      panel.viewModel.updateState(
        preedit: preedit,
        selRange: selRange,
        caretPos: caretPos,
        candidates: candidateItems.map { $0.outputText },
        comments: candidateItems.map { $0.proofreadText },
        labels: candidateItems.map { $0.label },
        highlightedIndex: highlighted
      )
      panel.show(at: inputPos)
    }
  }
}
