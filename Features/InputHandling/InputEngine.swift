import AppKit
import Foundation
import InputMethodKit
import OSLog

// MARK: - Key Code Constants

private enum KeyCode: UInt16 {
    case returnKey = 36
    case delete = 51
    case space = 49
    case leftArrow = 123
    case rightArrow = 124
    case escape = 53
    case tab = 48
    case upArrow = 126
    case downArrow = 125
    case leftShift = 56
    case rightShift = 60
}

// 自定义输入状态结构
struct InputState {
    /// 预编辑文本，即用户正在输入但尚未提交的内容
    var preedit: String = ""
    /// 候选词列表
    var candidateItems: [CandidateItem] = []
    /// 当前高亮候选词的索引
    var highlighted: Int = 0
    /// 预编辑文本中选择范围的起始位置
    var selStart: Int = 0
    /// 预编辑文本中选择范围的结束位置
    var selEnd: Int = 0
    /// 光标在预编辑文本中的位置
    var caretPos: Int = 0
}

// 自定义输入选项
struct InputOptions {
    /// 线形布局开关，可能指水平候选框
    var linear: Bool = false
    /// 垂直布局开关，用于候选框
    var vertical: Bool = false
    /// 是否在客户端内联显示预编辑文本
    var inlinePreedit: Bool = true
    /// 是否以内联方式预览所选候选词
    var inlineCandidate: Bool = false
}

// 输入引擎结果类型
struct InputEngineResult {
    let handled: Bool
    let shouldCommit: Bool
    let commitText: String?
    let shouldUpdateUI: Bool
    let shouldHidePalettes: Bool
    let shouldShowPanel: Bool
    let panelData: PanelData?

    init(handled: Bool = false,
         shouldCommit: Bool = false,
         commitText: String? = nil,
         shouldUpdateUI: Bool = false,
         shouldHidePalettes: Bool = false,
         shouldShowPanel: Bool = false,
         panelData: PanelData? = nil)
    {
        self.handled = handled
        self.shouldCommit = shouldCommit
        self.commitText = commitText
        self.shouldUpdateUI = shouldUpdateUI
        self.shouldHidePalettes = shouldHidePalettes
        self.shouldShowPanel = shouldShowPanel
        self.panelData = panelData
    }
}

// 面板数据结构
struct PanelData {
    let preedit: String
    let selRange: NSRange
    let caretPos: Int
    let candidateItems: [CandidateItem]
    let highlighted: Int
}

// 输入引擎协议
protocol InputEngineDelegate: AnyObject {
    func inputEngine(_ engine: InputEngine, didRequestCommit text: String)
    func inputEngine(_ engine: InputEngine, didRequestUpdateUI preedit: String, selRange: NSRange, caretPos: Int)
    func inputEngine(_ engine: InputEngine, didRequestShowPanel data: PanelData)
    func inputEngine(_ engine: InputEngine, didRequestHidePalettes: Bool)
}

// 输入引擎类
@MainActor
final class InputEngine {
    weak var delegate: InputEngineDelegate?

    private var inputState = InputState()
    private var options = InputOptions()
    private var sessionActive = false
    private var currentApp: String = ""

    private let logger = Logger(subsystem: "com.siflowtype.app", category: "InputEngine")

    // 注意：InputEngine 现在只处理 AI 模式下的按键事件
    // 英文模式下的按键事件由 SiflowtypeInputController 直接交给系统处理

    // 创建会话
    func createSession(for appBundleId: String) {
        currentApp = appBundleId
        sessionActive = true
        inputState = InputState()
        updateAppOptions()
        print("[InputEngine] Created session for app: \(appBundleId)")
    }

    // 销毁会话
    func destroySession() {
        sessionActive = false
        inputState = InputState()
        currentApp = ""
        print("[InputEngine] Destroyed session")
    }

    // 激活服务器
    func activateServer() {}

    // 提交组合
    func commitComposition() -> InputEngineResult {
        if sessionActive && !self.inputState.preedit.isEmpty {
            let text = self.inputState.preedit
            self.inputState = InputState()
            return InputEngineResult(handled: true, shouldCommit: true, commitText: text, shouldUpdateUI: true, shouldHidePalettes: true)
        }
        return InputEngineResult()
    }

    // 处理按键
    func handleKey(keyCode: UInt16, character: Character?, modifiers _: NSEvent.ModifierFlags, isRelease _: Bool) -> InputEngineResult {
        guard sessionActive else { return InputEngineResult() }

        // 同步面板选项
        syncPanelOptions()

        // InputEngine 只处理 AI 模式下的按键事件
        switch KeyCode(rawValue: keyCode) {
        case .returnKey:
            if !self.inputState.candidateItems.isEmpty && self.inputState.highlighted < self.inputState.candidateItems.count {
                // 如果有候选词，Enter键用于选择
                return selectCandidate(at: self.inputState.highlighted)
            } else if !self.inputState.preedit.isEmpty {
                // 如果没有候选词但有预编辑文本，Enter键触发AI
                if let panel = NSApp.siflowtypeAppDelegate.panel {
                    panel.viewModel.startAIGeneration()
                }
                generateCandidates()
                return InputEngineResult(handled: true)
            }
            return InputEngineResult()

        case .escape:
            if !self.inputState.preedit.isEmpty {
                if let panel = NSApp.siflowtypeAppDelegate.panel, panel.viewModel.uiState == .error {
                    panel.viewModel.reset()
                    panel.viewModel.preedit = self.inputState.preedit
                    panel.viewModel.uiState = .typing
                    return InputEngineResult(handled: true, shouldUpdateUI: true)
                } else {
                    self.inputState = InputState()
                    return InputEngineResult(handled: true, shouldUpdateUI: true, shouldHidePalettes: true)
                }
            }
            return InputEngineResult()

        case .delete:
            return handleBackspaceInAIMode()

        case .tab:
            if !self.inputState.candidateItems.isEmpty {
                // 如果有候选词，Tab键用于切换高亮
                self.inputState.highlighted = (self.inputState.highlighted + 1) % self.inputState.candidateItems.count
                return InputEngineResult(handled: true, shouldUpdateUI: true)
            }
            return InputEngineResult()

        case .space:
            if !self.inputState.candidateItems.isEmpty {
                return selectCandidate(at: self.inputState.highlighted)
            } else if !self.inputState.preedit.isEmpty {
                // 如果有预编辑文本，在预编辑文本中插入空格
                self.inputState.preedit.insert(" ", at: self.inputState.preedit.index(self.inputState.preedit.startIndex, offsetBy: self.inputState.caretPos))
                self.inputState.caretPos += 1
                return InputEngineResult(handled: true, shouldUpdateUI: true)
            } else {
                // 如果没有预编辑文本，让系统处理空格
                return InputEngineResult(handled: false)
            }

        case .leftArrow:
            return moveCaret(forward: true)

        case .rightArrow:
            return moveCaret(forward: false)

        case .downArrow:
            if !self.inputState.candidateItems.isEmpty {
                self.inputState.highlighted = min(self.inputState.highlighted + 1, self.inputState.candidateItems.count - 1)
                return InputEngineResult(handled: true, shouldUpdateUI: true)
            }
            return InputEngineResult()

        case .upArrow:
            if !self.inputState.candidateItems.isEmpty {
                self.inputState.highlighted = max(self.inputState.highlighted - 1, 0)
                return InputEngineResult(handled: true, shouldUpdateUI: true)
            }
            return InputEngineResult()

        case .leftShift, .rightShift:
            // Shift 键执行上屏操作
            if !self.inputState.preedit.isEmpty {
                let text = self.inputState.preedit
                self.inputState = InputState()
                print("[InputEngine] Shift key triggered commit: \(text)")
                return InputEngineResult(handled: true, shouldCommit: true, commitText: text, shouldUpdateUI: true, shouldHidePalettes: true)
            }
            return InputEngineResult()

        case .none:
            // 处理其他按键（字母、数字、标点符号等）
            if let char = character, char.isLetter || char.isNumber || char.isPunctuation {
                // AI模式：正常处理，添加到预编辑文本
                self.inputState.preedit.insert(char, at: self.inputState.preedit.index(self.inputState.preedit.startIndex, offsetBy: self.inputState.caretPos))
                self.inputState.caretPos += 1

                if let panel = NSApp.siflowtypeAppDelegate.panel {
                    panel.viewModel.updatePreedit(self.inputState.preedit)
                }

                return InputEngineResult(handled: true, shouldUpdateUI: true)
            }
            return InputEngineResult()
        }
    }

    // 选择候选词
    func selectCandidate(at index: Int) -> InputEngineResult {
        guard index >= 0 && index < self.inputState.candidateItems.count else {
            return InputEngineResult()
        }

        let selectedCandidate = self.inputState.candidateItems[index].outputText
        self.inputState = InputState()

        return InputEngineResult(handled: true, shouldCommit: true, commitText: selectedCandidate, shouldUpdateUI: true, shouldHidePalettes: true)
    }

    // 移动光标
    func moveCaret(forward: Bool) -> InputEngineResult {
        let preeditLength = self.inputState.preedit.count
        var moved = false

        if forward {
            if self.inputState.caretPos > 0 {
                self.inputState.caretPos -= 1
                moved = true
            }
        } else {
            if self.inputState.caretPos < preeditLength {
                self.inputState.caretPos += 1
                moved = true
            }
        }

        if moved {
            return InputEngineResult(handled: true, shouldUpdateUI: true)
        }

        return InputEngineResult()
    }

    // 获取当前状态
    func getCurrentState() -> (preedit: String, selRange: NSRange, caretPos: Int) {
        let selRange = NSRange(location: self.inputState.selStart, length: self.inputState.selEnd - self.inputState.selStart)
        return (self.inputState.preedit, selRange, self.inputState.caretPos)
    }

    // 获取候选词预览
    func getCandidatePreview() -> String? {
        if options.inlineCandidate, !self.inputState.candidateItems.isEmpty {
            return self.inputState.candidateItems[self.inputState.highlighted].outputText
        }
        return nil
    }

    // 是否应该显示面板
    func shouldShowPanel() -> Bool {
        // 只要有预编辑文本或候选词就应该显示面板
        return !self.inputState.preedit.isEmpty || !self.inputState.candidateItems.isEmpty
    }

    // 获取面板数据
    func getPanelData() -> PanelData? {
        // 只要有预编辑文本或候选词就返回面板数据
        guard !self.inputState.preedit.isEmpty || !self.inputState.candidateItems.isEmpty else { return nil }
        return createPanelData()
    }

    // 获取选项
    func getOptions() -> InputOptions {
        return options
    }

    // 更新选项
    func updateOptions(_ newOptions: InputOptions) {
        options = newOptions
    }
}

// MARK: - Private Methods

private extension InputEngine {
    /// 根据当前应用更新输入选项
    /// 此方法预留用于针对不同应用程序自定义输入行为
    /// 例如：某些应用可能需要特殊的候选词显示方式或输入模式
    func updateAppOptions() {
        // TODO: 根据 currentApp 的 Bundle ID 设置特定的输入选项
        // 例如：
        // switch currentApp {
        // case "com.apple.TextEdit":
        //     options.inlinePreedit = false
        // case "com.microsoft.Word":
        //     options.vertical = true
        // default:
        //     break
        // }
        if currentApp.isEmpty {
            return
        }
    }

    func syncPanelOptions() {
        if let panel = NSApp.siflowtypeAppDelegate.panel {
            if panel.linear != options.linear {
                options.linear = panel.linear
            }
            if panel.vertical != options.vertical {
                options.vertical = panel.vertical
            }
            options.inlinePreedit = panel.inlinePreedit
            options.inlineCandidate = panel.inlineCandidate
        }
    }

    func generateCandidates() {
        let currentText = self.inputState.preedit

        if currentText.isEmpty {
            self.inputState.candidateItems = []
            return
        }

        // 使用新的 CandidateService 替代旧的 OpenAIService
        Task { @MainActor in
            do {
                // 检查文本是否仍然是当前的预编辑文本（避免竞态条件）
                guard self.inputState.preedit == currentText else {
                    return
                }

                // 调用新的 CandidateService
                let suggestionPairs = try await AppContainer.shared.candidateService.fetchCandidates(for: currentText)

                // 再次检查文本是否仍然是当前的预编辑文本
                guard self.inputState.preedit == currentText else {
                    return
                }

                // 转换为 UI 所需的格式
                let outputTexts = suggestionPairs.map { $0.completed }
                let explanationTexts = suggestionPairs.map { $0.explanation }

                self.inputState.candidateItems = suggestionPairs.enumerated().map { index, pair in
                    CandidateItem(outputText: pair.completed, proofreadText: pair.explanation, label: "\(index + 1).")
                }
                self.inputState.highlighted = 0

                if let panel = NSApp.siflowtypeAppDelegate.panel {
                    panel.viewModel.updateWithCandidates(outputs: outputTexts, proofreads: explanationTexts)
                }

                self.delegate?.inputEngine(self, didRequestShowPanel: self.createPanelData())

            } catch let error as CandidateServiceError {
                self.logger.error("CandidateService error: \(error.localizedDescription)")
                self.handleGenerationError(error.localizedDescription, error.recoverySuggestion ?? "请重试。")

            } catch {
                self.logger.error("Unexpected error generating suggestions: \(error.localizedDescription)")
                self.handleGenerationError("请求失败，请重试。", "检查网络连接后重试。")
            }
        }
    }

    /// 处理生成错误的私有方法
    /// - Parameters:
    ///   - errorMessage: 英文错误消息
    ///   - chineseMessage: 中文错误消息
    private func handleGenerationError(_ errorMessage: String, _ chineseMessage: String) {
        self.inputState.candidateItems = [
            CandidateItem(outputText: errorMessage, proofreadText: chineseMessage, label: "1."),
        ]
        self.inputState.highlighted = 0

        if let panel = NSApp.siflowtypeAppDelegate.panel {
            panel.viewModel.updateWithError(message: errorMessage, chineseMessage: chineseMessage)
        }

        self.delegate?.inputEngine(self, didRequestShowPanel: self.createPanelData())
    }

    func createPanelData() -> PanelData {
        let selRange = NSRange(location: self.inputState.selStart, length: self.inputState.selEnd - self.inputState.selStart)
        let preedit = options.inlinePreedit ? "" : self.inputState.preedit

        return PanelData(preedit: preedit,
                         selRange: selRange,
                         caretPos: self.inputState.caretPos,
                         candidateItems: self.inputState.candidateItems,
                         highlighted: self.inputState.highlighted)
    }

    // MARK: - Mode-specific Key Handlers
    func handleBackspaceInAIMode() -> InputEngineResult {
        print("[InputEngine] handleBackspaceInAIMode called, preedit: '\(self.inputState.preedit)', caretPos: \(self.inputState.caretPos)")
        
        if !self.inputState.preedit.isEmpty {
            // 错误状态下，直接清空
            if let panel = NSApp.siflowtypeAppDelegate.panel, panel.viewModel.uiState == .error {
                print("[InputEngine] Error state detected, clearing all")
                self.inputState = InputState()
                panel.viewModel.reset()
                return InputEngineResult(handled: true, shouldUpdateUI: true, shouldHidePalettes: true)
            }

            let originalPreedit = self.inputState.preedit
            
            // 如果光标位置有效且在文本范围内
            if self.inputState.caretPos > 0 && self.inputState.caretPos <= self.inputState.preedit.count {
                print("[InputEngine] Removing character at position \(self.inputState.caretPos - 1)")
                let index = self.inputState.preedit.index(self.inputState.preedit.startIndex, offsetBy: self.inputState.caretPos - 1)
                self.inputState.preedit.remove(at: index)
                self.inputState.caretPos -= 1
            } else if self.inputState.caretPos == 0 && !self.inputState.preedit.isEmpty {
                // 光标在开头时，删除第一个字符
                print("[InputEngine] Removing first character")
                self.inputState.preedit.removeFirst()
            } else {
                // 其他情况，直接清空
                print("[InputEngine] Invalid caret position, clearing all")
                self.inputState = InputState()
                return InputEngineResult(handled: true, shouldUpdateUI: true, shouldHidePalettes: true)
            }

            print("[InputEngine] After deletion: '\(originalPreedit)' -> '\(self.inputState.preedit)'")

            // 检查删除后的状态
            if self.inputState.preedit.isEmpty {
                // 文本已空，重置状态并通知UI更新。
                // handled: true - 我们已经处理了退格，阻止系统再次处理。
                // shouldUpdateUI: true - 触发 SiflowtypeInputController.updateUI()，
                // 它将调用 client.setMarkedText("", ...)，从而清除 TextEdit 中的标记文本。
                print("[InputEngine] Preedit is now empty, resetting state and requesting UI update")
                self.inputState = InputState()
                let result = InputEngineResult(handled: true, shouldUpdateUI: true, shouldHidePalettes: true)
                print("[InputEngine] Returning result: handled=\(result.handled), shouldUpdateUI=\(result.shouldUpdateUI), shouldHidePalettes=\(result.shouldHidePalettes)")
                return result
            } else {
                // 文本不为空，更新UI
                print("[InputEngine] Preedit still has content, requesting UI update")
                let result = InputEngineResult(handled: true, shouldUpdateUI: true)
                print("[InputEngine] Returning result: handled=\(result.handled), shouldUpdateUI=\(result.shouldUpdateUI)")
                return result
            }
        }
        // 如果没有预编辑文本，让系统处理 Backspace
        print("[InputEngine] No preedit text, letting system handle backspace")
        return InputEngineResult(handled: false)
    }
}
