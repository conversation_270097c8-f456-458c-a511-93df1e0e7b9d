//
//  InputViewModel.swift
//  Siflowtype
//
//  Created by SwiftCoder on 2024/12/19.
//

import Combine
import Foundation
import os.log
import AppKit

/// 输入模式枚举
enum InputMode: String, CaseIterable {
    case ai = "ai"           // AI辅助模式（默认）
    case english = "english" // 纯英文直出模式
    
    /// 用户友好的显示名称
    var displayName: String {
        switch self {
        case .ai:
            return "AI 建议"
        case .english:
            return "纯英文"
        }
    }
    
    /// 模式切换UI提示显示的字符
    var indicatorText: String {
        switch self {
        case .ai:
            return "AI"
        case .english:
            return "EN"
        }
    }
}

/// InputViewModel的代理协议，用于与InputController解耦
protocol InputViewModelDelegate: AnyObject {
    /// 提交文本到当前应用
    func commitText(_ text: String)
    /// 显示模式切换UI提示
    func showModeIndicator(mode: InputMode)
    /// 通知输入模式变更
    func inputModeDidChange(to mode: InputMode)
}

/// InputViewModel - 负责输入模式管理和与InputController的协调
/// 专注于输入模式切换、按键处理和与Controller的通信
@MainActor
final class InputViewModel: ObservableObject {

    // MARK: - Published Properties
    
    /// 当前输入模式
    @Published var currentMode: InputMode = .ai

    // MARK: - Private Properties

    private let logger = Logger(subsystem: "com.siflowtype.app", category: "InputViewModel")
    private var cancellables = Set<AnyCancellable>()
    
    /// 代理，用于与InputController通信
    weak var delegate: InputViewModelDelegate?
    
    /// 候选词ViewModel的引用，用于委托管理
    weak var candidateViewModel: CandidateViewModel?
    
    /// UserDefaults键常量
    private static let inputModeKey = "SiflowtypeInputMode"

    // MARK: - Initialization

    init() {
        // 从UserDefaults加载保存的输入模式
        if let savedModeString = UserDefaults.standard.string(forKey: Self.inputModeKey),
           let savedMode = InputMode(rawValue: savedModeString) {
            self.currentMode = savedMode
        }
        
        setupBindings()
        setupNotificationObservers()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - Public Methods
    
    /// 设置候选词ViewModel的引用
    /// - Parameter candidateViewModel: 候选词ViewModel实例
    func setCandidateViewModel(_ candidateViewModel: CandidateViewModel) {
        self.candidateViewModel = candidateViewModel
        logger.debug("CandidateViewModel reference set")
    }
    
    /// 重置输入状态（委托给CandidateViewModel）
    func reset() {
        candidateViewModel?.reset()
        logger.debug("Delegated reset to CandidateViewModel")
    }
    
    // MARK: - Input Mode Management
    
    /// 切换输入模式
    func toggleInputMode() {
        let oldMode = currentMode
        let newMode: InputMode = (currentMode == .ai) ? .english : .ai
        

        
        setInputMode(newMode)
        
        // 显示模式切换UI提示
        delegate?.showModeIndicator(mode: newMode)
        

    }
    
    /// 设置输入模式
    func setInputMode(_ mode: InputMode) {
        currentMode = mode
        
        // 保存到UserDefaults
        UserDefaults.standard.set(mode.rawValue, forKey: Self.inputModeKey)
        
        // 通知代理模式变更
        delegate?.inputModeDidChange(to: mode)
        
        // 发送模式更改通知，用于更新菜单栏显示
        NotificationCenter.default.post(
            name: .inputModeDidChange,
            object: self,
            userInfo: ["mode": mode.rawValue, "displayName": mode.displayName]
        )
        
        // 如果切换到英文模式，重置候选词状态
        if mode == .english {
            candidateViewModel?.reset()
        }
    }
    
    /// 处理模式切换相关的按键事件
    /// - Parameter event: 按键事件
    /// - Returns: 如果事件被处理则返回true，否则返回false
    func handleModeToggleKeyEvent(_ event: NSEvent) -> Bool {
        let isShiftOnly = isShiftOnlyKeyEvent(event)
        let shouldShowBoard = candidateViewModel?.shouldShowBoard ?? false
        
        // 检查是否是Shift键单独按下（用于模式切换）
        if isShiftOnly && !shouldShowBoard {

            toggleInputMode()
            return true
        }
        

        return false
    }
    
    /// 检查是否是单独的Shift键按下事件
    private func isShiftOnlyKeyEvent(_ event: NSEvent) -> Bool {
        // 根据日志分析，事件类型是 flagsChanged (type=12)，这是修饰键状态改变事件
        let isFlagsChanged = event.type == .flagsChanged
        let isLeftShift = event.keyCode == 56 // 左Shift键码
        
        // 检查修饰键：当按下Shift时应该包含.shift标志
        let hasShiftModifier = event.modifierFlags.contains(.shift)
        
        // 确保只有Shift键被按下，没有其他修饰键
        let onlyShiftPressed = event.modifierFlags.intersection([.command, .option, .control]).isEmpty
        
        logger.debug("[ShiftCheck] 详细检查: isFlagsChanged=\(isFlagsChanged), isLeftShift=\(isLeftShift), hasShiftModifier=\(hasShiftModifier), onlyShiftPressed=\(onlyShiftPressed)")
        logger.debug("[ShiftCheck] 修饰键详情: raw=\(event.modifierFlags.rawValue), contains.shift=\(event.modifierFlags.contains(.shift))")
        
        let result = isFlagsChanged && isLeftShift && hasShiftModifier && onlyShiftPressed
        logger.debug("[ShiftCheck] 最终结果: \(result)")
        
        return result
    }

    // MARK: - Private Methods

    private func setupBindings() {
        // InputViewModel专注于输入模式管理，不再处理候选词相关的绑定
        // 候选词相关的绑定逻辑已移至CandidateViewModel
    }
    
    private func setupNotificationObservers() {
        // 监听来自菜单栏的模式切换请求
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleToggleInputModeNotification),
            name: .toggleInputMode,
            object: nil
        )
    }
    
    @objc private func handleToggleInputModeNotification(_ notification: Notification) {
        toggleInputMode()
    }
}

// MARK: - Convenience Methods

extension InputViewModel {
    
    /// 检查当前是否处于AI模式
    var isAIMode: Bool {
        return currentMode == .ai
    }
    
    /// 检查当前是否处于英文模式
    var isEnglishMode: Bool {
        return currentMode == .english
    }
}
