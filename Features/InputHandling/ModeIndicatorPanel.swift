//
//  ModeIndicatorPanel.swift
//  Siflowtype
//
//  Created by SwiftCoder on 2025/1/23.
//

import AppKit
import SwiftUI

/// 模式指示器面板
/// 负责管理模式指示器的显示和隐藏
@MainActor
final class ModeIndicatorPanel: NSPanel {
    private var hostingController: NSHostingController<ModeIndicatorView>?

    init(contentRect: NSRect) {
        super.init(
            contentRect: contentRect,
            styleMask: .nonactivatingPanel,
            backing: .buffered,
            defer: true
        )
        setupPanel()
    }
    
    convenience init() {
        self.init(contentRect: NSRect(x: 0, y: 0, width: 20, height: 20))
    }

    private func setupPanel() {
        // 参考 CandidatePanel 的配置
        level = .init(Int(CGShieldingWindowLevel())) // 使用与 CandidatePanel 相同的窗口层级
        backgroundColor = .clear
        isOpaque = false
        hasShadow = false // 移除阴影效果
        ignoresMouseEvents = true
        collectionBehavior = [.canJoinAllSpaces, .stationary] // 在所有空间显示且保持位置
    }

    // 重写只读属性以防止面板获得焦点
    override var canBecomeKey: Bool {
        return false
    }

    override var canBecomeMain: Bool {
        return false
    }

    /// 显示模式指示器
    /// - Parameters:
    ///   - mode: 输入模式
    ///   - inputPos: 输入位置（可选）
    func showModeIndicator(mode: InputMode, at inputPos: NSRect? = nil) {
        // 如果已经在显示，先隐藏
        if isVisible {
            hideModeIndicator()
        }

        // 创建SwiftUI视图和控制器
        let indicatorView = ModeIndicatorView(mode: mode)
        hostingController = NSHostingController(rootView: indicatorView)

        // 设置内容视图 - 参考 CandidatePanel 的方式
        setupSwiftUIView()

        // 获取屏幕信息
        guard let screen = NSScreen.main else {

            return
        }
        
        let screenFrame = screen.visibleFrame
        let panelSize = CGSize(width: 20, height: 20)
        
        // 计算显示位置
        let panelFrame: NSRect
        if let inputPos = inputPos, !inputPos.isEmpty {
            // 定位在光标下方
            let x = inputPos.origin.x
            let y = inputPos.origin.y - panelSize.height - 5 // 在光标下方5pt处

            // 确保面板在屏幕范围内
            let adjustedX = max(screenFrame.minX, min(x, screenFrame.maxX - panelSize.width))
            let adjustedY = max(screenFrame.minY, min(y, screenFrame.maxY - panelSize.height))

            panelFrame = NSRect(x: adjustedX, y: adjustedY, width: panelSize.width, height: panelSize.height)
        } else {
            // 默认居中显示
            let x = screenFrame.midX - panelSize.width / 2
            let y = screenFrame.midY - panelSize.height / 2
            panelFrame = NSRect(x: x, y: y, width: panelSize.width, height: panelSize.height)
        }
        
        // 设置面板位置和大小
        setFrame(panelFrame, display: true)

        // 设置透明度
        alphaValue = 1.0

        // 显示面板
        orderFront(nil)



        // 1秒后自动隐藏
        Task {
            try? await Task.sleep(nanoseconds: 1_000_000_000)
            await MainActor.run {
                self.hideModeIndicator()
            }
        }
    }
    
    /// 显示模式指示器（兼容旧接口）
    /// - Parameter mode: 输入模式
    func showModeIndicator(mode: InputMode) {
        showModeIndicator(mode: mode, at: nil)
    }

    /// 设置 SwiftUI 视图 - 参考 CandidatePanel 的实现
    private func setupSwiftUIView() {
        guard let hostingController = hostingController else { return }
        hostingController.view.translatesAutoresizingMaskIntoConstraints = false
        contentView = hostingController.view
    }

    /// 隐藏模式指示器
    private func hideModeIndicator() {
        orderOut(nil)
        hostingController = nil
        contentView = nil
    }
}
