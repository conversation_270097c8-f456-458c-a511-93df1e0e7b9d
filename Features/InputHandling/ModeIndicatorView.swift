//
//  ModeIndicatorView.swift
//  Siflowtype
//
//  Created by SwiftCoder on 2025/1/23.
//

import SwiftUI

/// 模式切换指示器视图
/// 在屏幕中央显示当前输入模式的简短提示
@MainActor
struct ModeIndicatorView: View {
    let mode: InputMode
    @State private var displayedMode: InputMode?
    @State private var isAnimating = false
    @State private var yOffset: CGFloat = 0
    @State private var opacity: Double = 1.0
    
    var body: some View {
        Text((displayedMode ?? mode).indicatorText)
            .font(.system(size: 10, weight: .bold, design: .rounded))
            .foregroundColor(.white)
            .frame(width: 20, height: 20)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.black.opacity(0.8))
            )
            .offset(y: yOffset)
            .opacity(opacity)
            .onAppear {
                displayedMode = mode
                startFlipAnimation()
            }
            .onChange(of: mode) { newMode in
                if !isAnimating {
                    startFlipAnimation(to: newMode)
                }
            }
    }
    
    private func startFlipAnimation(to newMode: InputMode? = nil) {
        guard !isAnimating else { return }

        if let newMode = newMode {
            // 模式切换动画
            isAnimating = true

            // 1. 旧视图向下移出并淡出
            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                yOffset = 20
                opacity = 0
            }

            // 2. 切换内容并准备新视图入场
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { // 减少延迟以加快过渡
                self.displayedMode = newMode
                self.yOffset = -20 // 新视图从下方准备

                // 3. 新视图向上移入并淡入
                withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                    self.yOffset = 0
                    self.opacity = 1
                }

                DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) {
                    isAnimating = false
                }
            }
        } else {
            // 初始显示动画
            opacity = 0
            yOffset = -10
            withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                opacity = 1
                yOffset = 0
            }
        }
    }
}

// MARK: - SwiftUI Previews
#Preview("AI Mode") {
    ModeIndicatorView(mode: .ai)
        .frame(width: 200, height: 200)
        .background(Color.gray.opacity(0.3))
}

#Preview("English Mode") {
    ModeIndicatorView(mode: .english)
        .frame(width: 200, height: 200)
        .background(Color.gray.opacity(0.3))
}
