//
//  CandidateViewModel.swift
//  Siflowtype
//
//  Created by Siflowtype on 2024.
//

import SwiftUI
import Foundation
import AppKit

// 候选词面板UI状态管理
enum CandidatePanelState {
    case typing               // 状态一：用户正在输入，只显示一个长条输入框
    case loading              // 状态二：用户按Tab，等待AI返回结果，显示加载中
    case displayingCandidates // 状态三：AI返回结果，展开显示候选列表
    case error                // 状态四：AI返回错误，显示错误信息
}

// 候选词视图模型，负责管理候选词状态、UI状态和用户交互
class CandidateViewModel: ObservableObject {
    // UI 状态属性
    @Published var uiState: CandidatePanelState = .typing // 当前 UI 状态
    
    @Published var preedit: String = "" // 预编辑文本
    @Published var selRange: NSRange = NSRange(location: NSNotFound, length: 0) // 选择范围
    @Published var caretPos: Int = 0 // 光标位置
    @Published var candidateItems: [CandidateItem] = [] // 候选词列表
    @Published var highlightedIndex: Int = 0 // 当前高亮候选词索引
    
    // 主题配置
    @Published var theme: Theme = Theme() // 主题样式配置
    
    // 更新状态的方法
    func updateState(
        preedit: String,
        selRange: NSRange,
        caretPos: Int,
        candidates: [String],
        comments: [String],
        labels: [String],
        highlightedIndex: Int
    ) {
        self.preedit = preedit
        self.selRange = selRange
        self.caretPos = caretPos
        
        self.candidateItems = candidates.indices.map { index in
            let output = candidates[index]
            let proofread = index < comments.count ? comments[index] : ""
            let label: String
            if labels.count > 1 && index < labels.count {
                label = labels[index]
            } else if labels.count == 1 && index < labels.first!.count {
                label = String(labels.first![labels.first!.index(labels.first!.startIndex, offsetBy: index)])
            } else {
                label = "\(index + 1)."
            }
            return CandidateItem(outputText: output, proofreadText: proofread, label: label)
        }
        
        self.highlightedIndex = highlightedIndex
    }
    
    // 检查是否有任何内容需要显示窗口
    var shouldShowBoard: Bool {
        // 只要有预编辑文本，窗口就应该显示
        !preedit.isEmpty
    }
    
    // 状态控制方法
    
    /// 当用户输入时，由外部调用来更新预编辑文本
    func updatePreedit(_ newPreedit: String) {
        // 只有在输入时才更新，避免在加载或显示结果时被覆盖
        if uiState == .typing {
            self.preedit = newPreedit
        }
    }

    /// 当用户按下 Tab 键，开始请求 AI
    func startAIGeneration() {
        guard !preedit.isEmpty else { return }
        // 清空旧的候选词
        self.candidateItems = []
        // 切换到加载状态
        self.uiState = .loading
    }
    
    /// 当 AI 返回结果时，调用此方法来更新 UI
    func updateWithCandidates(outputs: [String], proofreads: [String]) {
        self.candidateItems = zip(outputs, proofreads).enumerated().map { index, pair in
            CandidateItem(outputText: pair.0, proofreadText: pair.1, label: "\(index + 1).")
        }
        self.highlightedIndex = 0 // 默认高亮第一个
        
        // 使用动画切换到显示候选词的状态
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            self.uiState = .displayingCandidates
        }
    }
    
    /// 当 AI 返回错误时，调用此方法来更新 UI
    func updateWithError(message: String, chineseMessage: String) {
        self.candidateItems = [
            CandidateItem(outputText: message, proofreadText: chineseMessage, label: "1.")
        ]
        self.highlightedIndex = 0
        
        // 使用动画切换到错误状态
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            self.uiState = .error
        }
    }
    
    /// 当用户选择了一个候选词或取消了操作，重置回初始状态
    func reset() {
        self.preedit = ""
        self.candidateItems = []
        self.highlightedIndex = 0
        withAnimation(.easeOut(duration: 0.1)) {
            self.uiState = .typing
        }
    }
    
    // MARK: - 候选词交互方法
    
    /// 移动高亮索引
    /// - Parameter forward: true为向前，false为向后
    func moveHighlight(forward: Bool) {
        guard !candidateItems.isEmpty else { return }
        
        if forward {
            highlightedIndex = (highlightedIndex + 1) % candidateItems.count
        } else {
            highlightedIndex = highlightedIndex > 0 ? highlightedIndex - 1 : candidateItems.count - 1
        }
    }
    
    /// 选择候选词
    /// - Parameter index: 候选词索引
    /// - Returns: 选中的候选词文本，如果索引无效则返回nil
    func selectCandidate(at index: Int) -> String? {
        guard index >= 0 && index < candidateItems.count else {
            return nil
        }
        
        let selectedCandidate = candidateItems[index]
        
        // 选择后重置状态
        reset()
        
        return selectedCandidate.outputText
    }
    
    /// 获取当前高亮的候选词
    var currentHighlightedCandidate: CandidateItem? {
        guard highlightedIndex >= 0 && highlightedIndex < candidateItems.count else {
            return nil
        }
        return candidateItems[highlightedIndex]
    }
    
    /// 检查是否有候选词
    var hasCandidates: Bool {
        return !candidateItems.isEmpty
    }
    
    /// 检查是否有预编辑文本
    var hasPreedit: Bool {
        return !preedit.isEmpty
    }
}