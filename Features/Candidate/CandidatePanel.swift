//
//  CandidatePanel.swift
//  Siflowtype
//
//  Created by ervin on 2025/7/7.
//

import AppKit
import SwiftUI

final class CandidatePanel: NSPanel {
  let viewModel: CandidateViewModel
  private let hostingController: NSHostingController<CandidateBoardView>
  var inputController: SiflowtypeInputController?

  var position: NSRect
  private var screenRect: NSRect = .zero
  private var maxHeight: CGFloat = 0

  private var statusMessage: String = ""
  private var statusTimer: Timer?

  private var scrollDirection: CGVector = .zero
  private var scrollTime: Date = .distantPast
  private var pagingUp: Bool?

  init(position: NSRect) {
    self.position = position
    self.viewModel = CandidateViewModel()

    // 创建 SwiftUI 视图
    let candidateBoardView = CandidateBoardView(
      viewModel: viewModel,
      onCandidateSelected: nil,  // 稍后设置
      onPreeditClicked: nil
    )

    self.hostingController = NSHostingController(rootView: candidateBoardView)

    super.init(
      contentRect: position, styleMask: .nonactivatingPanel, backing: .buffered, defer: true)
    self.level = .init(Int(CGShieldingWindowLevel()))
    self.hasShadow = true
    self.isOpaque = false
    self.backgroundColor = .clear

    // 设置 SwiftUI 视图
    setupSwiftUIView()

    // 设置回调
    setupCallbacks()
  }

  var linear: Bool {
    viewModel.theme.linear
  }
  var vertical: Bool {
    viewModel.theme.vertical
  }
  var inlinePreedit: Bool {
    viewModel.theme.inlinePreedit
  }
  var inlineCandidate: Bool {
    viewModel.theme.inlineCandidate
  }

  override func sendEvent(_ event: NSEvent) {
    // switch event.type {
    // case .scrollWheel:
    //   handleScrollEvent(event)
    // default:
    //   break
    // }
    super.sendEvent(event)
  }

  private func handleScrollEvent(_ event: NSEvent) {
    if event.phase == .began {
      scrollDirection = .zero
    } else if event.phase == .ended
      || (event.phase == .init(rawValue: 0) && event.momentumPhase != .init(rawValue: 0))
    {
      scrollDirection = .zero
    } else if event.phase == .init(rawValue: 0) && event.momentumPhase == .init(rawValue: 0) {
      if scrollTime.timeIntervalSinceNow < -1 {
        scrollDirection = .zero
      }
      scrollTime = Date()
      if (scrollDirection.dy >= 0 && event.scrollingDeltaY > 0)
        || (scrollDirection.dy <= 0 && event.scrollingDeltaY < 0)
      {
        scrollDirection.dy += event.scrollingDeltaY
      } else {
        scrollDirection = .zero
      }
      if abs(scrollDirection.dy) > 10 {
        // 分页功能已移除
        scrollDirection = .zero
      }
    } else {
      scrollDirection.dx += event.scrollingDeltaX
      scrollDirection.dy += event.scrollingDeltaY
    }
  }

  func hide() {
    statusTimer?.invalidate()
    statusTimer = nil
    orderOut(nil)
    maxHeight = 0
  }

  // Main function to update the candidate panel
  func update(
    preedit: String, selRange: NSRange, caretPos: Int, candidates: [String], comments: [String],
    labels: [String], highlighted index: Int, update: Bool
  ) {
    if update {
      viewModel.updateState(
        preedit: preedit,
        selRange: selRange,
        caretPos: caretPos,
        candidates: candidates,
        comments: comments,
        labels: labels,
        highlightedIndex: index
      )
    }

    if !candidates.isEmpty || !preedit.isEmpty {
      statusMessage = ""
      statusTimer?.invalidate()
      statusTimer = nil
      show()
    } else {
      if !statusMessage.isEmpty {
        show(status: statusMessage)
        statusMessage = ""
      } else if statusTimer == nil {
        hide()
      }
      return
    }
  }

  func updateStatus(long longMessage: String, short shortMessage: String) {
    let theme = viewModel.theme
    switch theme.statusMessageType {
    case .mix:
      statusMessage = shortMessage.isEmpty ? longMessage : shortMessage
    case .long:
      statusMessage = longMessage
    case .short:
      if !shortMessage.isEmpty {
        statusMessage = shortMessage
      } else if let initial = longMessage.first {
        statusMessage = String(initial)
      } else {
        statusMessage = ""
      }
    }
  }

  func load() {
    // 加载主题配置
    viewModel.theme = Theme()
  }

  private func setupSwiftUIView() {
    hostingController.view.translatesAutoresizingMaskIntoConstraints = false
    self.contentView = hostingController.view
  }

  private func setupCallbacks() {
    // 更新 SwiftUI 视图的回调
    let candidateBoardView = CandidateBoardView(
      viewModel: viewModel,
      onCandidateSelected: { [weak self] index in
        _ = self?.inputController?.selectCandidate(index)
      },
      onPreeditClicked: { [weak self] index in
        guard let self = self else { return }
        if index < viewModel.caretPos {
          _ = self.inputController?.moveCaret(forward: true)
        } else if index > viewModel.caretPos {
          _ = self.inputController?.moveCaret(forward: false)
        }
      }
    )

    hostingController.rootView = candidateBoardView
  }
}

extension CandidatePanel {

  fileprivate func currentScreen() {
    if let screen = NSScreen.main {
      screenRect = screen.frame
    }
    for screen in NSScreen.screens where screen.frame.contains(position.origin) {
      screenRect = screen.frame
      break
    }
  }

  fileprivate func maxTextWidth() -> CGFloat {
    let theme = viewModel.theme
    let textWidthRatio = min(1.0, 1.0 / (vertical ? 4.0 : 3.0) + 1.0 / 12.0)
    let maxWidth =
      if vertical {
        screenRect.height * CGFloat(textWidthRatio) - theme.edgeInset.height * 2
      } else {
        screenRect.width * CGFloat(textWidthRatio) - theme.edgeInset.width * 2
      }
    return maxWidth
  }

  // 简化的显示方法
  func show() {
    show(at: nil)
  }
  
  func show(at inputPos: NSRect?) {
    currentScreen()
    let theme = viewModel.theme

    // 定义最大尺寸限制
    let maxWidth = min(0.95 * screenRect.width, 400)
    let maxHeight = min(0.95 * screenRect.height, 600) // 增加最大高度

    // 通过提供一个宽度限制，来获取视图换行后的正确尺寸
    let fittingSize = hostingController.sizeThatFits(
      in: NSSize(width: maxWidth, height: .greatestFiniteMagnitude)
    )

    // 使用计算出的尺寸，同时确保不超过最大值且不小于最小值
    let panelSize = NSSize(
      width: min(maxWidth, max(fittingSize.width, 200)),
      height: min(maxHeight, fittingSize.height)
    )

    // 计算面板位置
    var panelOrigin: NSPoint
    if let inputPos = inputPos, !inputPos.isEmpty {
      // 根据输入位置计算显示位置
      panelOrigin = NSPoint(
        x: inputPos.minX,
        y: inputPos.minY - panelSize.height - 10
      )
    } else {
      // 使用默认位置
      panelOrigin = NSPoint(
        x: position.minX,
        y: position.minY - panelSize.height - 10
      )
    }

    // 确保面板在屏幕范围内
    if panelOrigin.x + panelSize.width > screenRect.maxX {
      panelOrigin.x = screenRect.maxX - panelSize.width
    }
    if panelOrigin.x < screenRect.minX {
      panelOrigin.x = screenRect.minX
    }
    if panelOrigin.y < screenRect.minY {
      if let inputPos = inputPos, !inputPos.isEmpty {
        panelOrigin.y = inputPos.maxY + 10
      } else {
        panelOrigin.y = position.maxY + 10
      }
    }
    if panelOrigin.y + panelSize.height > screenRect.maxY {
      panelOrigin.y = screenRect.maxY - panelSize.height
    }

    let panelRect = NSRect(origin: panelOrigin, size: panelSize)
    self.setFrame(panelRect, display: true)

    alphaValue = theme.alpha
    orderFront(nil)
  }

  fileprivate func show(status message: String) {
    // 更新 ViewModel 以显示状态消息
    viewModel.updateState(
      preedit: "",
      selRange: NSRange(),
      caretPos: 0,
      candidates: [message],
      comments: [],
      labels: [],
      highlightedIndex: -1
    )

    show()

    statusTimer?.invalidate()
    statusTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: false) { _ in
      self.hide()
    }
  }
}