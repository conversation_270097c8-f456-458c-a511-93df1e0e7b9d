//
//  CandidateBoardView.swift
//  Siflowtype
//
//  Created by Siflowtype on 2024.
//

import SwiftUI

// 独立的输入栏视图
struct InputBarView: View {
    let preedit: String
    let uiState: CandidatePanelState
    let theme: Theme
    
    var body: some View {
        HStack(spacing: 8) {
            // 显示用户输入的文本
            Text(preedit)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color(theme.textColor))
                .lineLimit(1) // 确保单行显示
            
            Spacer()
            
            // 根据 UI 状态显示不同的尾部图标
            Group {
                switch uiState {
                case .typing:
                    // 输入状态：显示网格图标，暗示可触发
                    Image(systemName: "square.grid.2x2")
                        .font(.system(size: 14))
                        .foregroundColor(Color(theme.accentColor))
                        .transition(.opacity.combined(with: .scale))
                case .loading:
                    // 加载状态：显示一个旋转的菊花
                    ProgressView()
                        .controlSize(.small)
                        .transition(.opacity.combined(with: .scale))
                case .displayingCandidates:
                    // 显示结果时：显示网格图标
                    Image(systemName: "square.grid.2x2")
                        .font(.system(size: 14))
                        .foregroundColor(Color(theme.accentColor))
                case .error:
                    // 错误状态：显示警告图标
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.system(size: 14))
                        .foregroundColor(.orange)
                        .transition(.opacity.combined(with: .scale))
                }
            }
            .frame(width: 20, height: 20) // 给图标一个固定大小，防止布局跳动
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
    }
}

struct CandidateBoardView: View {
    @ObservedObject var viewModel: CandidateViewModel
    
    // 回调函数，用于处理用户交互
    var onCandidateSelected: ((Int) -> Void)?
    var onPreeditClicked: ((Int) -> Void)?
    
    init(
        viewModel: CandidateViewModel,
        onCandidateSelected: ((Int) -> Void)?,
        onPreeditClicked: ((Int) -> Void)?
    ) {
        self.viewModel = viewModel
        self.onCandidateSelected = onCandidateSelected
        self.onPreeditClicked = onPreeditClicked
    }
    
    var body: some View {
        // 只有在 shouldShowBoard 为 true 时才显示整个面板
        if viewModel.shouldShowBoard {
            // VStack 作为根容器，负责实现从长条到面板的"生长"效果
            VStack(alignment: .leading, spacing: 0) {
                // 统一的输入栏，始终显示
                InputBarView(
                    preedit: viewModel.preedit,
                    uiState: viewModel.uiState,
                    theme: viewModel.theme
                )
                
                // 在 displayingCandidates 或 error 状态下才显示候选列表
                if viewModel.uiState == .displayingCandidates || viewModel.uiState == .error {
                    // 添加分割线
                    Divider()
                        .padding(.horizontal, 12)
                        .padding(.vertical, 4)

                    // 候选词列表
                    candidatesView
                        .padding(.bottom, 4) // 给列表一些底部空间
                        .transition(.opacity) // 使用渐变动画
                }
            }
            // 整体样式应用到 VStack 上
            .background(
                RoundedRectangle(cornerRadius: viewModel.theme.cornerRadius)
                    .fill(Color(NSColor.controlBackgroundColor).opacity(0.8)) // 使用半透明背景兼容旧版本
                    .shadow(radius: viewModel.theme.shadowSize)
            )
            .overlay(
                RoundedRectangle(cornerRadius: viewModel.theme.cornerRadius)
                    .stroke(Color(viewModel.theme.borderColor), lineWidth: viewModel.theme.borderLineWidth)
            )
            .fixedSize(horizontal: false, vertical: true) // 让面板根据内容自适应大小
            .padding(10) // 留出阴影空间
        }
    }
    
    // 候选词列表视图 (基本不变, 仅微调样式)
    private var candidatesView: some View {
        // 使用 LazyVStack 提高性能
        LazyVStack(alignment: .leading, spacing: 4) {
            ForEach(viewModel.candidateItems.indices, id: \.self) { index in
                candidateRow(index: index, item: viewModel.candidateItems[index])
            }
        }
        .padding(.horizontal, 8) // 给列表左右一些内边距
    }
    
    // 单个候选词行 - 支持双行布局（英文+中文）
    private func candidateRow(index: Int, item: CandidateItem) -> some View {
        HStack(spacing: 8) {
            Text(item.label)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(
                    index == viewModel.highlightedIndex
                        ? Color(viewModel.theme.highlightedTextColor)
                        : Color(viewModel.theme.labelTextColor)
                )
                .frame(minWidth: 20, alignment: .center)
            
            // 双行布局：英文+中文
            VStack(alignment: .leading, spacing: 2) {
                // 第一行：英文句子
                Text(item.outputText)
                    .font(.system(size: 12))
                    .foregroundColor(
                        index == viewModel.highlightedIndex
                            ? Color(viewModel.theme.highlightedTextColor)
                            : Color(viewModel.theme.textColor)
                    )
                    .multilineTextAlignment(.leading)
                
                // 第二行：中文翻译（如果有）
                if !item.proofreadText.isEmpty {
                    Text(item.proofreadText)
                        .font(.system(size: 10))
                        .foregroundColor(
                            index == viewModel.highlightedIndex
                                ? Color(viewModel.theme.highlightedTextColor).opacity(0.8)
                                : Color(viewModel.theme.commentTextColor)
                        )
                        .multilineTextAlignment(.leading)
                }
            }
            
            Spacer()
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: viewModel.theme.hilitedCornerRadius)
                .fill(
                    viewModel.uiState == .error
                        ? Color.orange.opacity(0.2) // 错误状态使用橙色背景
                        : (index == viewModel.highlightedIndex
                            ? Color(viewModel.theme.highlightedBackgroundColor)
                            : .clear) // 非选中项背景透明
                )
        )
        .onTapGesture {
            onCandidateSelected?(index)
        }
    }
}

// 更新：预览视图
struct CandidateBoardView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 40) {
            // 场景1: 正在输入
            Group {
                Text("State: Typing").font(.caption)
                CandidateBoardView(viewModel: {
                    let viewModel = CandidateViewModel()
                    viewModel.preedit = "how to fix bug list not update"
                    viewModel.uiState = .typing
                    return viewModel
                }(), onCandidateSelected: nil, onPreeditClicked: nil)
            }
            
            // 场景2: 正在加载
            Group {
                Text("State: Loading").font(.caption)
                CandidateBoardView(viewModel: {
                    let viewModel = CandidateViewModel()
                    viewModel.preedit = "how to fix bug list not update"
                    viewModel.uiState = .loading
                    return viewModel
                }(), onCandidateSelected: nil, onPreeditClicked: nil)
            }

            // 场景3: 显示候选结果
            Group {
                Text("State: Displaying Candidates").font(.caption)
                CandidateBoardView(viewModel: {
                    let viewModel = CandidateViewModel()
                    viewModel.preedit = "how to fix bug list not update"
                    viewModel.candidateItems = [
                        CandidateItem(outputText: "How to fix the bug where the list doesn't update?", proofreadText: "如何修复列表不更新的错误？", label: "1."),
                        CandidateItem(outputText: "Fix the issue with the list not updating.", proofreadText: "修复列表不更新的问题。", label: "2."),
                        CandidateItem(outputText: "The list is not updating, what is the fix?", proofreadText: "列表没有更新，如何解决？", label: "3.")
                    ]
                    viewModel.uiState = .displayingCandidates
                    return viewModel
                }(), onCandidateSelected: nil, onPreeditClicked: nil)
            }
            
            // 场景4: 错误状态
            Group {
                Text("State: Error").font(.caption)
                CandidateBoardView(viewModel: {
                    let viewModel = CandidateViewModel()
                    viewModel.preedit = "how to fix bug list not update"
                    viewModel.candidateItems = [
                        CandidateItem(outputText: "Request failed. Please try again.", proofreadText: "请求失败，请重试。", label: "1.")
                    ]
                    viewModel.uiState = .error
                    return viewModel
                }(), onCandidateSelected: nil, onPreeditClicked: nil)
            }
        }
        .frame(width: 500)
        .padding()
    }
}
