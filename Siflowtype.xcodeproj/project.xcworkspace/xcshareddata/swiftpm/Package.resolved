{"originHash": "0f6fa789e3ccd649ba0dc0bf0e62d112b1bbe417fa1c42d661c1038d7c38a0a3", "pins": [{"identity": "openai", "kind": "remoteSourceControl", "location": "https://github.com/MacPaw/OpenAI.git", "state": {"branch": "main", "revision": "bf9aa18029cd22d39dad3e1f00dd40a5a4777f57"}}, {"identity": "swift-http-types", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-http-types", "state": {"revision": "a0a57e949a8903563aba4615869310c0ebf14c03", "version": "1.4.0"}}, {"identity": "swift-openapi-runtime", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-openapi-runtime", "state": {"revision": "8f33cc5dfe81169fb167da73584b9c72c3e8bc23", "version": "1.8.2"}}], "version": 3}