// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 70;
	objects = {

/* Begin PBXBuildFile section */
		11E81B35939D86A0581B4E4C /* InputEngine.swift in Sources */ = {isa = PBXBuildFile; fileRef = F00CE21C9B800CD0F86ED878 /* InputEngine.swift */; };
		2168607E419D5A317389E3CC /* Theme.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2B78731C32A2BEE9A650ADC0 /* Theme.swift */; };
		242CC48595D35492103C644B /* AuthenticationTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 852AF7122CAD6B91CB496046 /* AuthenticationTypes.swift */; };
		418B6C8EB23D63CC90D0A8EC /* CandidateItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A5A9F71DA74C6D56F3EFBC4 /* CandidateItem.swift */; };
		465920CAD2525C0F2739F8F6 /* SiflowtypeApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A64225E136B78F14B4B65835 /* SiflowtypeApp.swift */; };
		555A3D569B4455C2BE9B484D /* AuthService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3C147DF3C17556B5FFB8EBB9 /* AuthService.swift */; };
		5A20C342C607C1B7888CED24 /* OpenAI in Frameworks */ = {isa = PBXBuildFile; productRef = 1C564B519D0F41F19E0A991D /* OpenAI */; };
		79E3A0D43D886A48663EEACE /* CandidatePanel.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4E4B00159EB0E096B084DCF /* CandidatePanel.swift */; };
		9203AF7CE0E2B8A93EC0E17C /* CandidateBoardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A29B75E9DEE4C4AB000C987E /* CandidateBoardView.swift */; };
		991235E26971447EE4E52C98 /* CandidateViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9C25D6728B425484A233E686 /* CandidateViewModel.swift */; };
		A0A9CB4543DB01FB6A1EFD14 /* OpenAIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DFC59645F219DADA9BDF4F1 /* OpenAIService.swift */; };
		A641122C2E3C627E00F6F625 /* PromptConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = A64112282E3C627E00F6F625 /* PromptConfiguration.swift */; };
		A641122D2E3C627E00F6F625 /* ScenarioContext.swift in Sources */ = {isa = PBXBuildFile; fileRef = A64112292E3C627E00F6F625 /* ScenarioContext.swift */; };
		A641122E2E3C627E00F6F625 /* SuggestionModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = A641122A2E3C627E00F6F625 /* SuggestionModels.swift */; };
		A641122F2E3C627E00F6F625 /* GeneratorError.swift in Sources */ = {isa = PBXBuildFile; fileRef = A64112272E3C627E00F6F625 /* GeneratorError.swift */; };
		A64112512E3C62E000F6F625 /* OpenAIGenerator.swift in Sources */ = {isa = PBXBuildFile; fileRef = A641124E2E3C62E000F6F625 /* OpenAIGenerator.swift */; };
		A64112532E3C62E000F6F625 /* CandidateServiceProtocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = A641124D2E3C62E000F6F625 /* CandidateServiceProtocols.swift */; };
		A64112542E3C62E000F6F625 /* CandidateService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A641124C2E3C62E000F6F625 /* CandidateService.swift */; };
		A641125E2E3C9A0600F6F625 /* GeneralPromptProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = A641125D2E3C9A0600F6F625 /* GeneralPromptProvider.swift */; };
		A64112912E3CA59D00F6F625 /* SiflowtypeTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A641128E2E3CA59D00F6F625 /* SiflowtypeTests.swift */; };
		A64112A42E3CA92000F6F625 /* OpenAIGeneratorE2ETests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A641128D2E3CA59D00F6F625 /* OpenAIGeneratorE2ETests.swift */; };
		A64112A52E3CA93100F6F625 /* XCTest.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A64112872E3CA4FD00F6F625 /* XCTest.framework */; };
		A64112A62E3CA93100F6F625 /* XCTest.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = A64112872E3CA4FD00F6F625 /* XCTest.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		A64112A82E3CA94C00F6F625 /* OpenAIGeneratorE2ETests.swift in Sources */ = {isa = PBXBuildFile; fileRef = A641128D2E3CA59D00F6F625 /* OpenAIGeneratorE2ETests.swift */; };
		A6F728FD2E3B2357003B5C11 /* ModeIndicatorPanel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A6F728FC2E3B2357003B5C11 /* ModeIndicatorPanel.swift */; };
		B62DA38EF2A30A7EC44BB895 /* InputViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5534DD606C174C2C9BF2FCEA /* InputViewModel.swift */; };
		C134711870355BDE73D5127B /* AppCoordinator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11A464C85FC4D4873AB688AD /* AppCoordinator.swift */; };
		C2E2D856D10F68FDF35E162C /* AppEnvironment.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9D452C0009AAA90A09240849 /* AppEnvironment.swift */; };
		CE1ADF6228EE635CD042AFA4 /* NotificationNames.swift in Sources */ = {isa = PBXBuildFile; fileRef = D27A95F7D0C1CE207C785CB6 /* NotificationNames.swift */; };
		D85CC874779D677D52B0080B /* ModeIndicatorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 56F04BDE358D2B09C3151858 /* ModeIndicatorView.swift */; };
		E9C8F33766393BA1183E9958 /* SiflowtypeInputController.swift in Sources */ = {isa = PBXBuildFile; fileRef = AB7917C56CB9469DD8D25B87 /* SiflowtypeInputController.swift */; };
		F4CF2A23B5DB9AFF30FB3EBB /* AppContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 99959D12DC1A7E5C9A6400FC /* AppContainer.swift */; };
		FB11B0A622B39A2FB44485E5 /* SiflowtypeApplicationDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 366A6CCE1CF504588A8014B6 /* SiflowtypeApplicationDelegate.swift */; };
		FDC0211FC87E7ADDAA73EFAA /* KeychainService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9D6EC6FABE0DCE43A6E3E240 /* KeychainService.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		A641129F2E3CA91600F6F625 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = FFA3B3E4E08C201A46CBBF70 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B8663D6D3037F3D77CF0B170;
			remoteInfo = Siflowtype;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		A64112A72E3CA93100F6F625 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				A64112A62E3CA93100F6F625 /* XCTest.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		11A464C85FC4D4873AB688AD /* AppCoordinator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppCoordinator.swift; sourceTree = "<group>"; };
		2B78731C32A2BEE9A650ADC0 /* Theme.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Theme.swift; sourceTree = "<group>"; };
		366A6CCE1CF504588A8014B6 /* SiflowtypeApplicationDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SiflowtypeApplicationDelegate.swift; sourceTree = "<group>"; };
		3C147DF3C17556B5FFB8EBB9 /* AuthService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthService.swift; sourceTree = "<group>"; };
		5534DD606C174C2C9BF2FCEA /* InputViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InputViewModel.swift; sourceTree = "<group>"; };
		56F04BDE358D2B09C3151858 /* ModeIndicatorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModeIndicatorView.swift; sourceTree = "<group>"; };
		7E093B76498F1A29410EB314 /* Siflowtype.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Siflowtype.app; sourceTree = BUILT_PRODUCTS_DIR; };
		852AF7122CAD6B91CB496046 /* AuthenticationTypes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AuthenticationTypes.swift; sourceTree = "<group>"; };
		8A5A9F71DA74C6D56F3EFBC4 /* CandidateItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CandidateItem.swift; sourceTree = "<group>"; };
		8DFC59645F219DADA9BDF4F1 /* OpenAIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OpenAIService.swift; sourceTree = "<group>"; };
		99959D12DC1A7E5C9A6400FC /* AppContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppContainer.swift; sourceTree = "<group>"; };
		9C25D6728B425484A233E686 /* CandidateViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CandidateViewModel.swift; sourceTree = "<group>"; };
		9D452C0009AAA90A09240849 /* AppEnvironment.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppEnvironment.swift; sourceTree = "<group>"; };
		9D6EC6FABE0DCE43A6E3E240 /* KeychainService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeychainService.swift; sourceTree = "<group>"; };
		A29B75E9DEE4C4AB000C987E /* CandidateBoardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CandidateBoardView.swift; sourceTree = "<group>"; };
		A6389AF32E3B4EEB00FF7C20 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A6389AF42E3B4EEB00FF7C20 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A6389AF52E3B4EEB00FF7C20 /* InfoPlist.xcstrings */ = {isa = PBXFileReference; lastKnownFileType = text.json.xcstrings; path = InfoPlist.xcstrings; sourceTree = "<group>"; };
		A6389AF62E3B4EEB00FF7C20 /* MenuIcon.icns */ = {isa = PBXFileReference; lastKnownFileType = image.icns; path = MenuIcon.icns; sourceTree = "<group>"; };
		A6389AF72E3B4EEB00FF7C20 /* siflowtype.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = siflowtype.pdf; sourceTree = "<group>"; };
		A6389AF82E3B4EEB00FF7C20 /* Siflowtype.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Siflowtype.entitlements; sourceTree = "<group>"; };
		A64112272E3C627E00F6F625 /* GeneratorError.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GeneratorError.swift; sourceTree = "<group>"; };
		A64112282E3C627E00F6F625 /* PromptConfiguration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PromptConfiguration.swift; sourceTree = "<group>"; };
		A64112292E3C627E00F6F625 /* ScenarioContext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScenarioContext.swift; sourceTree = "<group>"; };
		A641122A2E3C627E00F6F625 /* SuggestionModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuggestionModels.swift; sourceTree = "<group>"; };
		A641124C2E3C62E000F6F625 /* CandidateService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CandidateService.swift; sourceTree = "<group>"; };
		A641124D2E3C62E000F6F625 /* CandidateServiceProtocols.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CandidateServiceProtocols.swift; sourceTree = "<group>"; };
		A641124E2E3C62E000F6F625 /* OpenAIGenerator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OpenAIGenerator.swift; sourceTree = "<group>"; };
		A641125D2E3C9A0600F6F625 /* GeneralPromptProvider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GeneralPromptProvider.swift; sourceTree = "<group>"; };
		A64112832E3CA4D300F6F625 /* Testing.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Testing.framework; path = Platforms/MacOSX.platform/Developer/Library/Frameworks/Testing.framework; sourceTree = DEVELOPER_DIR; };
		A64112872E3CA4FD00F6F625 /* XCTest.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = XCTest.framework; path = Platforms/MacOSX.platform/Developer/Library/Frameworks/XCTest.framework; sourceTree = DEVELOPER_DIR; };
		A641128D2E3CA59D00F6F625 /* OpenAIGeneratorE2ETests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OpenAIGeneratorE2ETests.swift; sourceTree = "<group>"; };
		A641128E2E3CA59D00F6F625 /* SiflowtypeTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SiflowtypeTests.swift; sourceTree = "<group>"; };
		A641129B2E3CA91600F6F625 /* SiflowtypeTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SiflowtypeTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		A64225E136B78F14B4B65835 /* SiflowtypeApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SiflowtypeApp.swift; sourceTree = "<group>"; };
		A6F728FC2E3B2357003B5C11 /* ModeIndicatorPanel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ModeIndicatorPanel.swift; sourceTree = "<group>"; };
		AB7917C56CB9469DD8D25B87 /* SiflowtypeInputController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SiflowtypeInputController.swift; sourceTree = "<group>"; };
		C4E4B00159EB0E096B084DCF /* CandidatePanel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CandidatePanel.swift; sourceTree = "<group>"; };
		D27A95F7D0C1CE207C785CB6 /* NotificationNames.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationNames.swift; sourceTree = "<group>"; };
		F00CE21C9B800CD0F86ED878 /* InputEngine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InputEngine.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		A641129C2E3CA91600F6F625 /* SiflowtypeTests */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = SiflowtypeTests; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		143E260700EEA66FDDEC95C9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				5A20C342C607C1B7888CED24 /* OpenAI in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A64112982E3CA91600F6F625 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				A64112A52E3CA93100F6F625 /* XCTest.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0239733240A315333D253586 /* Products */ = {
			isa = PBXGroup;
			children = (
				7E093B76498F1A29410EB314 /* Siflowtype.app */,
				A641129B2E3CA91600F6F625 /* SiflowtypeTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1FD3C9D7FE40E2616B4FFC33 /* InputHandling */ = {
			isa = PBXGroup;
			children = (
				A6F728FC2E3B2357003B5C11 /* ModeIndicatorPanel.swift */,
				F00CE21C9B800CD0F86ED878 /* InputEngine.swift */,
				5534DD606C174C2C9BF2FCEA /* InputViewModel.swift */,
				56F04BDE358D2B09C3151858 /* ModeIndicatorView.swift */,
				AB7917C56CB9469DD8D25B87 /* SiflowtypeInputController.swift */,
			);
			path = InputHandling;
			sourceTree = "<group>";
		};
		2FDB27CF235951638FA56FA2 /* Services */ = {
			isa = PBXGroup;
			children = (
				A641125D2E3C9A0600F6F625 /* GeneralPromptProvider.swift */,
				A641124C2E3C62E000F6F625 /* CandidateService.swift */,
				A641124D2E3C62E000F6F625 /* CandidateServiceProtocols.swift */,
				A641124E2E3C62E000F6F625 /* OpenAIGenerator.swift */,
				852AF7122CAD6B91CB496046 /* AuthenticationTypes.swift */,
				3C147DF3C17556B5FFB8EBB9 /* AuthService.swift */,
				9D6EC6FABE0DCE43A6E3E240 /* KeychainService.swift */,
				8DFC59645F219DADA9BDF4F1 /* OpenAIService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		91DDF9CF8F89E162B19E87E1 /* Features */ = {
			isa = PBXGroup;
			children = (
				DF018912EAF42B44935F6A6F /* Candidate */,
				1FD3C9D7FE40E2616B4FFC33 /* InputHandling */,
			);
			path = Features;
			sourceTree = "<group>";
		};
		A6389AF92E3B4EEB00FF7C20 /* Resources */ = {
			isa = PBXGroup;
			children = (
				A6389AF32E3B4EEB00FF7C20 /* Assets.xcassets */,
				A6389AF42E3B4EEB00FF7C20 /* Info.plist */,
				A6389AF52E3B4EEB00FF7C20 /* InfoPlist.xcstrings */,
				A6389AF62E3B4EEB00FF7C20 /* MenuIcon.icns */,
				A6389AF72E3B4EEB00FF7C20 /* siflowtype.pdf */,
				A6389AF82E3B4EEB00FF7C20 /* Siflowtype.entitlements */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		A641122B2E3C627E00F6F625 /* Models */ = {
			isa = PBXGroup;
			children = (
				A64112272E3C627E00F6F625 /* GeneratorError.swift */,
				A64112282E3C627E00F6F625 /* PromptConfiguration.swift */,
				A64112292E3C627E00F6F625 /* ScenarioContext.swift */,
				A641122A2E3C627E00F6F625 /* SuggestionModels.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A64112822E3CA4D300F6F625 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				A64112872E3CA4FD00F6F625 /* XCTest.framework */,
				A64112832E3CA4D300F6F625 /* Testing.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A641128F2E3CA59D00F6F625 /* Tests */ = {
			isa = PBXGroup;
			children = (
				A641128D2E3CA59D00F6F625 /* OpenAIGeneratorE2ETests.swift */,
				A641128E2E3CA59D00F6F625 /* SiflowtypeTests.swift */,
			);
			path = Tests;
			sourceTree = "<group>";
		};
		AB99BE37F8ECEE4E6B01F937 = {
			isa = PBXGroup;
			children = (
				A641128F2E3CA59D00F6F625 /* Tests */,
				A641122B2E3C627E00F6F625 /* Models */,
				A6389AF92E3B4EEB00FF7C20 /* Resources */,
				ED1D6DAE91B43AFBF96CFD78 /* Application */,
				91DDF9CF8F89E162B19E87E1 /* Features */,
				2FDB27CF235951638FA56FA2 /* Services */,
				F00DD4A4874D1C96B4CDED22 /* Utilities */,
				A641129C2E3CA91600F6F625 /* SiflowtypeTests */,
				0239733240A315333D253586 /* Products */,
				A64112822E3CA4D300F6F625 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		DF018912EAF42B44935F6A6F /* Candidate */ = {
			isa = PBXGroup;
			children = (
				A29B75E9DEE4C4AB000C987E /* CandidateBoardView.swift */,
				8A5A9F71DA74C6D56F3EFBC4 /* CandidateItem.swift */,
				C4E4B00159EB0E096B084DCF /* CandidatePanel.swift */,
				9C25D6728B425484A233E686 /* CandidateViewModel.swift */,
			);
			path = Candidate;
			sourceTree = "<group>";
		};
		ED1D6DAE91B43AFBF96CFD78 /* Application */ = {
			isa = PBXGroup;
			children = (
				99959D12DC1A7E5C9A6400FC /* AppContainer.swift */,
				11A464C85FC4D4873AB688AD /* AppCoordinator.swift */,
				9D452C0009AAA90A09240849 /* AppEnvironment.swift */,
				A64225E136B78F14B4B65835 /* SiflowtypeApp.swift */,
				366A6CCE1CF504588A8014B6 /* SiflowtypeApplicationDelegate.swift */,
			);
			path = Application;
			sourceTree = "<group>";
		};
		F00DD4A4874D1C96B4CDED22 /* Utilities */ = {
			isa = PBXGroup;
			children = (
				D27A95F7D0C1CE207C785CB6 /* NotificationNames.swift */,
				2B78731C32A2BEE9A650ADC0 /* Theme.swift */,
			);
			path = Utilities;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A641129A2E3CA91600F6F625 /* SiflowtypeTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A64112A12E3CA91600F6F625 /* Build configuration list for PBXNativeTarget "SiflowtypeTests" */;
			buildPhases = (
				A64112972E3CA91600F6F625 /* Sources */,
				A64112982E3CA91600F6F625 /* Frameworks */,
				A64112992E3CA91600F6F625 /* Resources */,
				A64112A72E3CA93100F6F625 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				A64112A02E3CA91600F6F625 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				A641129C2E3CA91600F6F625 /* SiflowtypeTests */,
			);
			name = SiflowtypeTests;
			packageProductDependencies = (
			);
			productName = SiflowtypeTests;
			productReference = A641129B2E3CA91600F6F625 /* SiflowtypeTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		B8663D6D3037F3D77CF0B170 /* Siflowtype */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2994AC0139A20C55ACFDB691 /* Build configuration list for PBXNativeTarget "Siflowtype" */;
			buildPhases = (
				047ADCEDAD649875C508230B /* Sources */,
				143E260700EEA66FDDEC95C9 /* Frameworks */,
				A6389AF22E3B4ECE00FF7C20 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Siflowtype;
			packageProductDependencies = (
				1C564B519D0F41F19E0A991D /* OpenAI */,
			);
			productName = Siflowtype;
			productReference = 7E093B76498F1A29410EB314 /* Siflowtype.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		FFA3B3E4E08C201A46CBBF70 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					A641129A2E3CA91600F6F625 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = B8663D6D3037F3D77CF0B170;
					};
					B8663D6D3037F3D77CF0B170 = {
						DevelopmentTeam = 4MQ3AWP5NR;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 20BACC082D2CA2A577D34987 /* Build configuration list for PBXProject "Siflowtype" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = AB99BE37F8ECEE4E6B01F937;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				B81D76ADA887E055C094C412 /* XCRemoteSwiftPackageReference "OpenAI" */,
			);
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B8663D6D3037F3D77CF0B170 /* Siflowtype */,
				A641129A2E3CA91600F6F625 /* SiflowtypeTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A6389AF22E3B4ECE00FF7C20 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A64112992E3CA91600F6F625 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		047ADCEDAD649875C508230B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F4CF2A23B5DB9AFF30FB3EBB /* AppContainer.swift in Sources */,
				C134711870355BDE73D5127B /* AppCoordinator.swift in Sources */,
				C2E2D856D10F68FDF35E162C /* AppEnvironment.swift in Sources */,
				555A3D569B4455C2BE9B484D /* AuthService.swift in Sources */,
				242CC48595D35492103C644B /* AuthenticationTypes.swift in Sources */,
				9203AF7CE0E2B8A93EC0E17C /* CandidateBoardView.swift in Sources */,
				A64112912E3CA59D00F6F625 /* SiflowtypeTests.swift in Sources */,
				418B6C8EB23D63CC90D0A8EC /* CandidateItem.swift in Sources */,
				A641122C2E3C627E00F6F625 /* PromptConfiguration.swift in Sources */,
				A641122D2E3C627E00F6F625 /* ScenarioContext.swift in Sources */,
				A641122E2E3C627E00F6F625 /* SuggestionModels.swift in Sources */,
				A641122F2E3C627E00F6F625 /* GeneratorError.swift in Sources */,
				79E3A0D43D886A48663EEACE /* CandidatePanel.swift in Sources */,
				991235E26971447EE4E52C98 /* CandidateViewModel.swift in Sources */,
				A64112A82E3CA94C00F6F625 /* OpenAIGeneratorE2ETests.swift in Sources */,
				A6F728FD2E3B2357003B5C11 /* ModeIndicatorPanel.swift in Sources */,
				11E81B35939D86A0581B4E4C /* InputEngine.swift in Sources */,
				B62DA38EF2A30A7EC44BB895 /* InputViewModel.swift in Sources */,
				FDC0211FC87E7ADDAA73EFAA /* KeychainService.swift in Sources */,
				D85CC874779D677D52B0080B /* ModeIndicatorView.swift in Sources */,
				A64112512E3C62E000F6F625 /* OpenAIGenerator.swift in Sources */,
				A64112532E3C62E000F6F625 /* CandidateServiceProtocols.swift in Sources */,
				A64112542E3C62E000F6F625 /* CandidateService.swift in Sources */,
				CE1ADF6228EE635CD042AFA4 /* NotificationNames.swift in Sources */,
				A0A9CB4543DB01FB6A1EFD14 /* OpenAIService.swift in Sources */,
				465920CAD2525C0F2739F8F6 /* SiflowtypeApp.swift in Sources */,
				FB11B0A622B39A2FB44485E5 /* SiflowtypeApplicationDelegate.swift in Sources */,
				A641125E2E3C9A0600F6F625 /* GeneralPromptProvider.swift in Sources */,
				E9C8F33766393BA1183E9958 /* SiflowtypeInputController.swift in Sources */,
				2168607E419D5A317389E3CC /* Theme.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		A64112972E3CA91600F6F625 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				A64112A42E3CA92000F6F625 /* OpenAIGeneratorE2ETests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		A64112A02E3CA91600F6F625 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B8663D6D3037F3D77CF0B170 /* Siflowtype */;
			targetProxy = A641129F2E3CA91600F6F625 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		5825DF9AA8C94B7A597185CE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = Resources/Siflowtype.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 12;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Siflowtype;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 0.1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.siflowtype.inputmethod.Siflowtype;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		A64112A22E3CA91600F6F625 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 4MQ3AWP5NR;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GENERATE_INFOPLIST_FILE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.siflowtype.SiflowtypeTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Siflowtype.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Siflowtype";
			};
			name = Debug;
		};
		A64112A32E3CA91600F6F625 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 4MQ3AWP5NR;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GENERATE_INFOPLIST_FILE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.siflowtype.SiflowtypeTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Siflowtype.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Siflowtype";
			};
			name = Release;
		};
		BAEBB0EDAC1E1EEDD762B187 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 4MQ3AWP5NR;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		BF34006B36B4134FD2C793E0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 4MQ3AWP5NR;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		D1FCC1503995B809202ADB6B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_ENTITLEMENTS = Resources/Siflowtype.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 12;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Siflowtype;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 0.1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.siflowtype.inputmethod.Siflowtype;
				SDKROOT = macosx;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		20BACC082D2CA2A577D34987 /* Build configuration list for PBXProject "Siflowtype" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BAEBB0EDAC1E1EEDD762B187 /* Debug */,
				BF34006B36B4134FD2C793E0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		2994AC0139A20C55ACFDB691 /* Build configuration list for PBXNativeTarget "Siflowtype" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5825DF9AA8C94B7A597185CE /* Debug */,
				D1FCC1503995B809202ADB6B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		A64112A12E3CA91600F6F625 /* Build configuration list for PBXNativeTarget "SiflowtypeTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A64112A22E3CA91600F6F625 /* Debug */,
				A64112A32E3CA91600F6F625 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		B81D76ADA887E055C094C412 /* XCRemoteSwiftPackageReference "OpenAI" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/MacPaw/OpenAI.git";
			requirement = {
				branch = main;
				kind = branch;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		1C564B519D0F41F19E0A991D /* OpenAI */ = {
			isa = XCSwiftPackageProductDependency;
			package = B81D76ADA887E055C094C412 /* XCRemoteSwiftPackageReference "OpenAI" */;
			productName = OpenAI;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = FFA3B3E4E08C201A46CBBF70 /* Project object */;
}
