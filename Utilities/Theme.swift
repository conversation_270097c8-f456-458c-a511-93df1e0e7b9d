//
//  Theme.swift
//  Siflowtype
//
//  Created by Siflowtype on 2024.
//

import SwiftUI
import Foundation
import AppKit

// 状态消息类型枚举
enum StatusMessageType {
    case mix
    case long
    case short
}

// 主题样式配置
struct Theme {
    var edgeInset: NSSize = NSSize(width: 10, height: 10)
    var cornerRadius: CGFloat = 8
    var borderWidth: CGFloat = 1
    var backgroundColor: NSColor = NSColor.controlBackgroundColor
    var borderColor: NSColor = NSColor.separatorColor
    var textColor: NSColor = NSColor.labelColor
    var highlightedBackgroundColor: NSColor = NSColor.selectedContentBackgroundColor
    var highlightedTextColor: NSColor = NSColor.white
    var commentTextColor: NSColor = NSColor.secondaryLabelColor
    var labelTextColor: NSColor = NSColor.tertiaryLabelColor
    var fontSize: CGFloat = 14
    var spacing: CGFloat = 4

    // Properties from error messages
    var linespace: CGFloat = 4
    var preeditLinespace: CGFloat = 4
    var borderLineWidth: CGFloat = 1
    var shadowSize: CGFloat = 2
    var hilitedCornerRadius: CGFloat = 4
    var candidateBackColor: NSColor = NSColor.windowBackgroundColor
    var accentColor: NSColor = NSColor.controlAccentColor

    init() {
        // 默认初始化
    }
}

// 扩展 Theme 以包含更多属性
extension Theme {
    var linear: Bool { false }
    var vertical: Bool { false }
    var inlinePreedit: Bool { false }
    var inlineCandidate: Bool { false }
    var alpha: CGFloat { 0.95 }
    var statusMessageType: StatusMessageType { .mix }
}