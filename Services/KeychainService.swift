//
//  KeychainService.swift
//  Siflowtype
//
//  Created by SwiftCoder on 2025/7/22.
//

import Foundation
import Security
import OSLog

/// 钥匙串服务，用于安全存储敏感信息
class KeychainService {
    private let service: String
    private let logger = Logger(subsystem: "com.siflowtype.app", category: "KeychainService")
    
    init(service: String = "com.siflowtype.auth") {
        self.service = service
    }
    
    /// 存储数据到钥匙串
    /// - Parameters:
    ///   - value: 要存储的值
    ///   - key: 存储的键
    func store(_ value: String, for key: String) {
        guard let data = value.data(using: .utf8) else {
            logger.error("无法将值转换为数据: \(key)")
            return
        }
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data
        ]
        
        // 先删除已存在的项目
        SecItemDelete(query as CFDictionary)
        
        // 添加新项目
        let status = SecItemAdd(query as CFDictionary, nil)
        
        if status == errSecSuccess {
            logger.info("成功存储到钥匙串: \(key)")
        } else {
            logger.error("存储到钥匙串失败: \(key), 状态码: \(status)")
        }
    }
    
    /// 从钥匙串检索数据
    /// - Parameter key: 要检索的键
    /// - Returns: 检索到的值，如果不存在则返回 nil
    func retrieve(for key: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        if status == errSecSuccess {
            if let data = result as? Data,
               let value = String(data: data, encoding: .utf8) {
                logger.debug("成功从钥匙串检索: \(key)")
                return value
            }
        } else if status == errSecItemNotFound {
            logger.debug("钥匙串中未找到项目: \(key)")
        } else {
            logger.error("从钥匙串检索失败: \(key), 状态码: \(status)")
        }
        
        return nil
    }
    
    /// 从钥匙串删除数据
    /// - Parameter key: 要删除的键
    func delete(for key: String) {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key
        ]
        
        let status = SecItemDelete(query as CFDictionary)
        
        if status == errSecSuccess {
            logger.info("成功从钥匙串删除: \(key)")
        } else if status == errSecItemNotFound {
            logger.debug("钥匙串中未找到要删除的项目: \(key)")
        } else {
            logger.error("从钥匙串删除失败: \(key), 状态码: \(status)")
        }
    }
    
    /// 检查钥匙串中是否存在指定的键
    /// - Parameter key: 要检查的键
    /// - Returns: 如果存在返回 true，否则返回 false
    func exists(for key: String) -> Bool {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        let status = SecItemCopyMatching(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    /// 清除所有存储的认证信息
    func clearAllAuthData() {
        logger.info("清除所有认证数据")
        
        let authKeys = ["accessToken", "refreshToken", "tokenExpiration"]
        
        for key in authKeys {
            delete(for: key)
        }
    }
}