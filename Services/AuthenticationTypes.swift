//
//  AuthenticationTypes.swift
//  Siflowtype
//
//  Created by SwiftCoder on 2025/7/22.
//

import Foundation
import CryptoKit

// MARK: - 认证服务错误
enum AuthServiceError: Error, LocalizedError {
    case invalidResponse
    case networkError(Error)
    case tokenExchangeFailed(String)
    case pkceGenerationFailed
    case invalidAuthorizationURL
    case missingCodeVerifier
    case userInfoFetchFailed
    case tokenExpired
    case invalidToken
    
    var errorDescription: String? {
        switch self {
        case .invalidResponse:
            return "无效的服务器响应"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .tokenExchangeFailed(let message):
            return "令牌交换失败: \(message)"
        case .pkceGenerationFailed:
            return "PKCE 生成失败"
        case .invalidAuthorizationURL:
            return "无效的授权 URL"
        case .missingCodeVerifier:
            return "缺少 code_verifier"
        case .userInfoFetchFailed:
            return "获取用户信息失败"
        case .tokenExpired:
            return "访问令牌已过期"
        case .invalidToken:
            return "无效的访问令牌"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .networkError:
            return "请检查网络连接后重试"
        case .tokenExchangeFailed, .tokenExpired, .invalidToken:
            return "请重新登录"
        case .pkceGenerationFailed, .invalidAuthorizationURL:
            return "请联系技术支持"
        default:
            return "请稍后重试"
        }
    }
}

// MARK: - 令牌响应模型
struct TokenResponse: Codable {
    let accessToken: String
    let refreshToken: String?
    let expiresIn: Int
    let tokenType: String
    
    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case refreshToken = "refresh_token"
        case expiresIn = "expires_in"
        case tokenType = "token_type"
    }
}

// MARK: - PKCE (Proof Key for Code Exchange)
struct PKCE {
    let codeVerifier: String
    let codeChallenge: String

    init?() {
        // 1. 生成一个随机的 code_verifier
        var buffer = [UInt8](repeating: 0, count: 32)
        let status = SecRandomCopyBytes(kSecRandomDefault, buffer.count, &buffer)
        guard status == errSecSuccess else { return nil }
        self.codeVerifier = Data(buffer).base64URLEncodedString()

        // 2. 基于 verifier 生成 SHA256 哈希的 code_challenge
        guard let verifierData = codeVerifier.data(using: .utf8) else { return nil }
        let challengeData = SHA256.hash(data: verifierData)
        self.codeChallenge = Data(challengeData).base64URLEncodedString()
    }
}

// MARK: - Data Extensions
extension Data {
    func base64URLEncodedString() -> String {
        return self.base64EncodedString()
            .replacingOccurrences(of: "+", with: "-")
            .replacingOccurrences(of: "/", with: "_")
            .replacingOccurrences(of: "=", with: "")
    }
}

/// 用户信息模型
struct User: Codable, Identifiable {
    let id: String
    let username: String
    let email: String
    let avatarURL: URL?

    enum CodingKeys: String, CodingKey {
        case id
        case username
        case email
        case avatarURL = "avatar_url"
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let didLoginSuccessfully = Notification.Name("didLoginSuccessfully")
    static let didLogout = Notification.Name("didLogout")
    static let authenticationError = Notification.Name("authenticationError")
}