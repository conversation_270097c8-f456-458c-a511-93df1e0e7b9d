//
//  AuthService.swift
//  Siflowtype
//
//  Created by SwiftCoder on 2025/7/22.
//

import Foundation
import OSLog
import AppKit

/// 认证服务
class AuthService: ObservableObject {
    private let logger = Logger(subsystem: "com.siflowtype.app", category: "AuthService")
    private let keychainService: KeychainService
    private let config: AppEnvironmentConfiguration
    
    private var pkce: PKCE?

    init(config: AppEnvironmentConfiguration, keychainService: KeychainService) {
        self.config = config
        self.keychainService = keychainService
    }
    
   
}

