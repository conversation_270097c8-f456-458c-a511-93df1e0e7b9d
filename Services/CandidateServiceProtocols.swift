//
//  CandidateServiceProtocols.swift
//  Siflowtype
//
//  Created by Ervin on 2024/8/1.
//

import Foundation
import OpenAI

// MARK: - 核心协议定义

/// 提示词提供者协议
/// 
/// 负责根据场景上下文构建完整的提示词配置。
/// 这种设计允许灵活地从不同来源（本地模板、远程服务等）获取和构建提示词。
protocol PromptProvider {
    /// 为指定的场景上下文构建提示词配置
    /// - Parameter context: 场景上下文
    /// - Returns: 完整的提示词配置
    /// - Throws: 如果构建配置失败则抛出错误
    func buildConfiguration(for context: ScenarioContext) async throws -> PromptConfiguration
}

/// 候选词生成器协议
/// 
/// 负责执行实际的 AI 调用，包含完整的超时和重试逻辑。
/// 这种设计将 AI 调用逻辑与业务逻辑分离，提高了可测试性和可维护性。
protocol CandidateGenerator {
    /// 为给定的文本生成结构化响应
    /// - Parameters:
    ///   - text: 用户输入的文本
    ///   - config: 提示词配置
    ///   - temperature: AI 模型的温度参数（0.0-1.0）
    ///   - timeout: 请求超时时间（秒）
    ///   - retries: 最大重试次数
    /// - Returns: 解码后的响应对象
    /// - Throws: 如果生成失败则抛出 GeneratorError
    func generate(
        for text: String, 
        with config: PromptConfiguration,
        temperature: Double,
        timeout: TimeInterval,
        retries: Int
    ) async throws -> CandidateResponse
}


