# Siflowtype - AI 英文输入法

Siflowtype 是一款专为开发者设计的 AI 英文输入法。它的核心任务是解决非英语母语者在与 AI 编程助手（如 Cursor, Claude, Gemini-cli）进行英文对话时的表达障碍。用户无需再为语法、措辞或句子结构而烦恼，只需随意输入碎片化的想法，Siflowtype 就能实时将其转化为语法正确、表达地道的高质量英文句子。

## ✨ 功能特性

- **实时智能润色**: 在任意应用中输入你的想法（中英混合、关键词、代码片段），Siflowtype 会自动将其转换为流畅、专业的英文句子。
- **无缝集成**: 作为一款输入法，它能无感地融入你已有的工作流，无需在不同应用间切换。
- **多候选词**: 提供多个润色后的选项，你可以通过方向键或数字键快速选择最合适的表达。
- **macOS 原生体验**: 遵循 Apple 的设计规范，提供与系统一致的、自适应深浅色模式的 UI 体验。

## 📖 系统设计与文档

本项目的完整产品需求、UI/UX 设计规范和模块化系统设计文档都存放于项目的 `/wiki` 目录中。我们遵循“在代码之前，先在文档中构建系统”的理念，将 `/wiki` 作为项目唯一的真实信源 (Single Source of Truth)。

- **[查看系统设计概览](./wiki/00_系统概览.md)**
- **[查看产品需求文档](./wiki/产品需求.md)**
- **[查看UI/UX设计规范](./wiki/01_UI&UX设计规范.md)**

欢迎通过 `archie show overview` 命令来动态查看最新的模块依赖关系图。

## 🚀 如何开始

(待补充：项目构建、开发和贡献指南)




# 菜单栏图标生成
          
从 SVG 生成一个适用于 macOS 菜单栏的 `.icns` 文件，推荐遵循 Apple 的标准工作流程。这个流程可以确保你的图标在标准和 Retina 显示器上都能清晰地显示。

以下是具体步骤：

### 步骤 1: 从 SVG 导出所需尺寸的 PNG 图片

你需要将你的 SVG logo 导出为多个不同尺寸的 PNG 文件。对于一个标准的菜单栏图标，你需要至少以下两个尺寸。文件名必须严格遵守 `icon_<尺寸>x<尺寸>[@2x].png` 的格式。

- `icon_16x16.png` (尺寸: 16x16 像素)
- `<EMAIL>` (尺寸: 32x32 像素)

**重要提示**: 菜单栏图标应该是“模板图像”（Template Image）。这意味着它应该只包含黑色和透明色。macOS 会自动处理颜色，根据菜单栏的背景（亮色或暗色）将其显示为黑色或白色。

你可以使用任何矢量图形编辑软件（如 Sketch, Figma, Adobe Illustrator）来导出这些 PNG 文件。

### 步骤 2: 创建 `iconset` 文件夹

1.  创建一个新文件夹，并将其命名为 `MenuIcon.iconset` (或者任何你喜欢的名字，但必须以 `.iconset` 结尾)。
2.  将上一步中导出的所有 PNG 文件（`icon_16x16.png` 和 `<EMAIL>`）放入这个 `MenuIcon.iconset` 文件夹中。

### 步骤 3: 使用 `iconutil` 命令行工具生成 `.icns` 文件

1.  打开“终端” (Terminal.app)。
2.  使用 `cd` 命令进入到包含 `MenuIcon.iconset` 文件夹的目录。
3.  运行以下命令：

    ```bash
    iconutil -c icns MenuIcon.iconset
    ```

这个命令会读取 `MenuIcon.iconset` 文件夹，并生成一个名为 `MenuIcon.icns` 的文件在当前目录下。

### 步骤 4: 在 Xcode 中使用新的 `.icns` 文件

1.  将新生成的 `MenuIcon.icns` 文件拖入到你的 Xcode 项目的 `Resources` 文件夹中，替换掉旧的文件。
2.  确保该文件已添加到你的应用 Target 的 `Build Phases` -> `Copy Bundle Resources` 中。
3.  重新编译并运行你的应用，新的图标应该就会正确显示了。
        